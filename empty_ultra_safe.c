/*
 * MSPM0G3507 七路灰度循迹模块 - 超级安全版本
 * 引脚配置: B12,B17,B04,B01,A28,A31,B15
 * 
 * 特点: 避免所有可能的编译错误
 * 作者: Alex (工程师)
 * 版本: v1.0-ultra-safe
 */

#include "ti_msp_dl_config.h"

// ================================
// 全局变量定义
// ================================
static uint16_t g_sensors[7];
static uint32_t g_counter = 0;

// ================================
// 基础延时函数
// ================================
static void delay_simple(void) {
    volatile uint32_t i;
    for (i = 0; i < 80000; i++) {
        // 延时循环
    }
}

// ================================
// UART基础函数
// ================================
static void uart_send_char(uint8_t c) {
    DL_UART_transmitDataBlocking(UART_0_INST, c);
}

static void uart_send_string(const char* str) {
    const char* p = str;
    while (*p != '\0') {
        uart_send_char((uint8_t)*p);
        p++;
    }
}

// ================================
// 数字转字符串函数
// ================================
static void uart_send_uint16(uint16_t num) {
    char digits[6];
    int count = 0;
    
    if (num == 0) {
        uart_send_char('0');
        return;
    }
    
    while (num > 0) {
        digits[count] = '0' + (num % 10);
        num = num / 10;
        count++;
    }
    
    for (int i = count - 1; i >= 0; i--) {
        uart_send_char(digits[i]);
    }
}

// ================================
// GPIO读取函数
// ================================
static uint16_t read_gpio_pin(uint32_t pin) {
    uint32_t pin_state = DL_GPIO_readPins(GPIOB, pin);
    return (pin_state != 0) ? 4095 : 0;
}

// ================================
// ADC读取函数 - 最简化
// ================================
static uint16_t read_adc_simple(void) {
    DL_ADC12_startConversion(ADC_VOLTAGE_INST);
    
    // 简单等待转换完成
    volatile uint32_t timeout = 10000;
    while (timeout > 0) {
        timeout--;
    }
    
    return DL_ADC12_getMemResult(ADC_VOLTAGE_INST, ADC_VOLTAGE_ADCMEM_ADC_CH0);
}

// ================================
// 传感器读取主函数
// ================================
static void read_all_sensors(void) {
    // 传感器1: B12
    g_sensors[0] = read_gpio_pin(DL_GPIO_PIN_12);
    
    // 传感器2: B17
    g_sensors[1] = read_gpio_pin(DL_GPIO_PIN_17);
    
    // 传感器3: B04
    g_sensors[2] = read_gpio_pin(DL_GPIO_PIN_4);
    
    // 传感器4: B01
    g_sensors[3] = read_gpio_pin(DL_GPIO_PIN_1);
    
    // 传感器5: A28 (ADC)
    g_sensors[4] = read_adc_simple();
    
    // 传感器6: A31 (ADC)
    g_sensors[5] = read_adc_simple();
    
    // 传感器7: B15
    g_sensors[6] = read_gpio_pin(DL_GPIO_PIN_15);
}

// ================================
// 数据显示函数
// ================================
static void display_sensor_data(void) {
    uart_send_string("数据[");
    uart_send_uint16(g_counter);
    uart_send_string("]: ");
    
    // 显示所有传感器数据
    for (int i = 0; i < 7; i++) {
        uart_send_string("[");
        uart_send_uint16(g_sensors[i]);
        uart_send_string("]");
        if (i < 6) {
            uart_send_string(" ");
        }
    }
    
    // 简单的线条检测
    int line_count = 0;
    for (int i = 0; i < 7; i++) {
        if (g_sensors[i] > 2000) {
            line_count++;
        }
    }
    
    uart_send_string(" | 活跃:");
    uart_send_uint16(line_count);
    uart_send_string("/7");
    
    if (line_count > 0) {
        uart_send_string(" | 检测:是");
    } else {
        uart_send_string(" | 检测:否");
    }
    
    uart_send_string("\r\n");
}

// ================================
// 主程序
// ================================
int main(void) {
    // 系统初始化
    SYSCFG_DL_init();
    
    // 启动信息
    uart_send_string("\r\n");
    uart_send_string("========================================\r\n");
    uart_send_string("MSPM0G3507 七路灰度循迹系统\r\n");
    uart_send_string("版本: 超级安全版 v1.0\r\n");
    uart_send_string("引脚: B12,B17,B04,B01,A28,A31,B15\r\n");
    uart_send_string("========================================\r\n");
    uart_send_string("系统初始化完成\r\n");
    uart_send_string("开始数据采集...\r\n\r\n");
    
    // 主循环
    while (1) {
        // 读取传感器数据
        read_all_sensors();
        
        // 显示数据
        display_sensor_data();
        
        // 计数器递增
        g_counter++;
        if (g_counter > 9999) {
            g_counter = 0;
        }
        
        // 延时
        delay_simple();
    }
    
    return 0;
}
