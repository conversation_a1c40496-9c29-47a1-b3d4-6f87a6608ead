/*
 * Copyright (c) 2025, MSPM0G3507 GrayScale Line Following Module
 * All rights reserved.
 *
 * 七路灰度循迹模块 - 核心实现
 * 基于MSPM0G3507嘉立创天猛星开发板
 * 
 * 作者: <PERSON> (工程师)
 * 版本: v1.0
 * 日期: 2025-07-10
 */

#include "grayscale_line_following.h"
#include <stdio.h>
#include <math.h>

// ================================
// 内部全局变量
// ================================
static GrayScale_Handle_t* g_grayscale_handle = NULL;  // 全局句柄指针
static volatile bool g_adc_conversion_complete = false; // ADC转换完成标志

// 默认配置参数
static const GrayScale_Config_t DEFAULT_CONFIG = {
    .sampling_frequency = GRAYSCALE_SAMPLING_FREQ,
    .filter_window_size = GRAYSCALE_FILTER_WINDOW_SIZE,
    .detection_threshold = DETECTION_THRESHOLD_DEFAULT,
    .sensor_weights = {-3.0f, -2.0f, -1.0f, 0.0f, 1.0f, 2.0f, 3.0f}, // 传感器位置权重
    .pid_kp = 1.0f,
    .pid_ki = 0.0f,
    .pid_kd = 0.1f,
    .auto_calibration = false
};

// ADC通道映射表
static const uint8_t ADC_CHANNEL_MAP[GRAYSCALE_SENSOR_COUNT] = {
    0, 1, 2, 3, 4, 5, 6  // CH0-CH6
};

// GPIO引脚映射表
static const uint32_t GPIO_PIN_MAP[GRAYSCALE_SENSOR_COUNT] = {
    DL_GPIO_PIN_27, DL_GPIO_PIN_26, DL_GPIO_PIN_25, DL_GPIO_PIN_24,
    DL_GPIO_PIN_23, DL_GPIO_PIN_22, DL_GPIO_PIN_21
};

// ================================
// 内部辅助函数声明
// ================================
static int _GrayScale_InitHardware(void);
static int _GrayScale_ConfigureADC(void);
static int _GrayScale_ConfigureGPIO(void);
static void _GrayScale_StartNextConversion(GrayScale_Handle_t* handle);
static void _GrayScale_ProcessRawData(GrayScale_Handle_t* handle);
static void _GrayScale_ApplyDigitalFilter(GrayScale_Handle_t* handle);
static float _GrayScale_CalculateLinePosition(const GrayScale_Handle_t* handle);
static void _GrayScale_UpdatePID(GrayScale_Handle_t* handle, float position_error);

// ================================
// 核心API函数实现
// ================================

/**
 * @brief 初始化灰度循迹系统
 * @param handle 系统句柄指针
 * @return 错误代码
 */
int GrayScale_Init(GrayScale_Handle_t* handle) {
    if (!handle) {
        return GRAYSCALE_ERROR_PARAM;
    }
    
    // 清零句柄结构
    memset(handle, 0, sizeof(GrayScale_Handle_t));
    
    // 设置默认配置
    handle->config = DEFAULT_CONFIG;
    handle->state = GRAYSCALE_STATE_INITIALIZED;
    
    // 初始化硬件
    int result = _GrayScale_InitHardware();
    if (result != GRAYSCALE_SUCCESS) {
        handle->state = GRAYSCALE_STATE_ERROR;
        return result;
    }
    
    // 设置全局句柄指针
    g_grayscale_handle = handle;
    
    // 初始化调试信息
    handle->debug_info.total_samples = 0;
    handle->debug_info.error_count = 0;
    handle->debug_info.max_processing_time = 0;
    handle->debug_info.avg_processing_time = 0;
    
    return GRAYSCALE_SUCCESS;
}

/**
 * @brief 反初始化灰度循迹系统
 * @param handle 系统句柄指针
 * @return 错误代码
 */
int GrayScale_DeInit(GrayScale_Handle_t* handle) {
    if (!handle) {
        return GRAYSCALE_ERROR_PARAM;
    }
    
    // 停止采样
    GrayScale_StopSampling(handle);
    
    // 禁用ADC中断
    NVIC_DisableIRQ(ADC_VOLTAGE_INST_INT_IRQN);
    
    // 重置状态
    handle->state = GRAYSCALE_STATE_UNINITIALIZED;
    g_grayscale_handle = NULL;
    
    return GRAYSCALE_SUCCESS;
}

/**
 * @brief 开始数据采样
 * @param handle 系统句柄指针
 * @return 错误代码
 */
int GrayScale_StartSampling(GrayScale_Handle_t* handle) {
    if (!handle || handle->state != GRAYSCALE_STATE_INITIALIZED) {
        return GRAYSCALE_ERROR_NOT_INITIALIZED;
    }
    
    // 重置采样状态
    handle->current_channel = 0;
    handle->conversion_complete = false;
    handle->current_data.data_ready = false;
    
    // 启用ADC中断
    NVIC_EnableIRQ(ADC_VOLTAGE_INST_INT_IRQN);
    
    // 开始第一个通道的转换
    _GrayScale_StartNextConversion(handle);
    
    handle->state = GRAYSCALE_STATE_RUNNING;
    return GRAYSCALE_SUCCESS;
}

/**
 * @brief 停止数据采样
 * @param handle 系统句柄指针
 * @return 错误代码
 */
int GrayScale_StopSampling(GrayScale_Handle_t* handle) {
    if (!handle) {
        return GRAYSCALE_ERROR_PARAM;
    }
    
    // 禁用ADC中断
    NVIC_DisableIRQ(ADC_VOLTAGE_INST_INT_IRQN);
    
    // 停止ADC转换
    DL_ADC12_stopConversion(ADC_VOLTAGE_INST);
    
    handle->state = GRAYSCALE_STATE_INITIALIZED;
    return GRAYSCALE_SUCCESS;
}

/**
 * @brief 获取传感器数据
 * @param handle 系统句柄指针
 * @param data 数据输出指针
 * @return 错误代码
 */
int GrayScale_GetData(GrayScale_Handle_t* handle, GrayScale_Data_t* data) {
    if (!handle || !data) {
        return GRAYSCALE_ERROR_PARAM;
    }
    
    if (!handle->current_data.data_ready) {
        return GRAYSCALE_ERROR_BUSY;
    }
    
    // 复制数据
    *data = handle->current_data;
    
    return GRAYSCALE_SUCCESS;
}

/**
 * @brief 检查数据是否就绪
 * @param handle 系统句柄指针
 * @return true: 数据就绪, false: 数据未就绪
 */
bool GrayScale_IsDataReady(GrayScale_Handle_t* handle) {
    return handle ? handle->current_data.data_ready : false;
}

/**
 * @brief 处理循迹算法
 * @param handle 系统句柄指针
 * @return 错误代码
 */
int GrayScale_ProcessLineFollowing(GrayScale_Handle_t* handle) {
    if (!handle || !handle->current_data.data_ready) {
        return GRAYSCALE_ERROR_NOT_INITIALIZED;
    }
    
    uint32_t start_time = DL_Common_getTimerCount(GPTIMER_0_INST); // 性能监控开始
    
    // 应用数字滤波
    _GrayScale_ApplyDigitalFilter(handle);
    
    // 计算线条位置
    float line_position = _GrayScale_CalculateLinePosition(handle);
    handle->line_result.line_position = line_position;
    
    // 计算位置误差
    float position_error = line_position - LINE_POSITION_CENTER;
    handle->line_result.position_error = position_error;
    
    // 更新PID控制器
    _GrayScale_UpdatePID(handle, position_error);
    
    // 检测线条是否存在
    uint8_t active_sensors = 0;
    for (int i = 0; i < GRAYSCALE_SENSOR_COUNT; i++) {
        if (handle->current_data.filtered_values[i] > handle->config.detection_threshold) {
            active_sensors++;
        }
    }
    handle->line_result.active_sensors = active_sensors;
    handle->line_result.line_detected = (active_sensors > 0);
    
    // 性能监控结束
    uint32_t end_time = DL_Common_getTimerCount(GPTIMER_0_INST);
    uint32_t processing_time = end_time - start_time;
    
    // 更新调试信息
    handle->debug_info.total_samples++;
    if (processing_time > handle->debug_info.max_processing_time) {
        handle->debug_info.max_processing_time = processing_time;
    }
    
    // 计算平均处理时间 (简单移动平均)
    handle->debug_info.avg_processing_time = 
        (handle->debug_info.avg_processing_time * 7 + processing_time) / 8;
    
    return GRAYSCALE_SUCCESS;
}

/**
 * @brief 获取线条位置
 * @param handle 系统句柄指针
 * @param position 位置输出指针
 * @return 错误代码
 */
int GrayScale_GetLinePosition(GrayScale_Handle_t* handle, float* position) {
    if (!handle || !position) {
        return GRAYSCALE_ERROR_PARAM;
    }
    
    *position = handle->line_result.line_position;
    return GRAYSCALE_SUCCESS;
}

/**
 * @brief 获取控制输出
 * @param handle 系统句柄指针
 * @param output 控制输出指针
 * @return 错误代码
 */
int GrayScale_GetControlOutput(GrayScale_Handle_t* handle, float* output) {
    if (!handle || !output) {
        return GRAYSCALE_ERROR_PARAM;
    }
    
    *output = handle->line_result.control_output;
    return GRAYSCALE_SUCCESS;
}

/**
 * @brief 获取错误字符串
 * @param error 错误代码
 * @return 错误描述字符串
 */
const char* GrayScale_GetErrorString(GrayScale_Error_t error) {
    switch (error) {
        case GRAYSCALE_SUCCESS: return "Success";
        case GRAYSCALE_ERROR_PARAM: return "Parameter Error";
        case GRAYSCALE_ERROR_HARDWARE: return "Hardware Error";
        case GRAYSCALE_ERROR_TIMEOUT: return "Timeout Error";
        case GRAYSCALE_ERROR_BUSY: return "System Busy";
        case GRAYSCALE_ERROR_NOT_INITIALIZED: return "Not Initialized";
        case GRAYSCALE_ERROR_CALIBRATION: return "Calibration Error";
        default: return "Unknown Error";
    }
}

// ================================
// 内部辅助函数实现
// ================================

/**
 * @brief 初始化硬件
 * @return 错误代码
 */
static int _GrayScale_InitHardware(void) {
    int result;

    // 配置GPIO
    result = _GrayScale_ConfigureGPIO();
    if (result != GRAYSCALE_SUCCESS) {
        return result;
    }

    // 配置ADC
    result = _GrayScale_ConfigureADC();
    if (result != GRAYSCALE_SUCCESS) {
        return result;
    }

    return GRAYSCALE_SUCCESS;
}

/**
 * @brief 配置GPIO引脚
 * @return 错误代码
 */
static int _GrayScale_ConfigureGPIO(void) {
    // 配置7个GPIO引脚为ADC模拟输入
    for (int i = 0; i < GRAYSCALE_SENSOR_COUNT; i++) {
        // 配置引脚为模拟输入模式
        DL_GPIO_initPeripheralAnalogFunction(
            (IOMUX_PINCM22 + (6-i)), // IOMUX配置 (PA21-PA27)
            DL_GPIO_INVERSION_DISABLE
        );
    }

    return GRAYSCALE_SUCCESS;
}

/**
 * @brief 配置ADC
 * @return 错误代码
 */
static int _GrayScale_ConfigureADC(void) {
    // ADC时钟配置
    static const DL_ADC12_ClockConfig adc_clock_config = {
        .clockSel = DL_ADC12_CLOCK_SYSOSC,
        .divideRatio = DL_ADC12_CLOCK_DIVIDE_8,
        .freqRange = DL_ADC12_CLOCK_FREQ_RANGE_24_TO_32,
    };

    // 设置ADC时钟
    DL_ADC12_setClockConfig(ADC_VOLTAGE_INST, (DL_ADC12_ClockConfig*)&adc_clock_config);

    // 初始化ADC为单次采样模式
    DL_ADC12_initSingleSample(ADC_VOLTAGE_INST,
        DL_ADC12_REPEAT_MODE_DISABLED,      // 禁用重复模式，手动控制
        DL_ADC12_SAMPLING_SOURCE_AUTO,      // 自动采样
        DL_ADC12_TRIG_SRC_SOFTWARE,         // 软件触发
        DL_ADC12_SAMP_CONV_RES_12_BIT,      // 12位分辨率
        DL_ADC12_SAMP_CONV_DATA_FORMAT_UNSIGNED // 无符号数据格式
    );

    // 配置所有7个ADC通道
    for (int i = 0; i < GRAYSCALE_SENSOR_COUNT; i++) {
        DL_ADC12_configConversionMem(ADC_VOLTAGE_INST,
            (DL_ADC12_MEM_IDX_0 + i),           // 内存索引
            (DL_ADC12_INPUT_CHAN_0 + i),        // 输入通道
            DL_ADC12_REFERENCE_VOLTAGE_VDDA,    // 参考电压
            DL_ADC12_SAMPLE_TIMER_SOURCE_SCOMP0, // 采样定时器
            DL_ADC12_AVERAGING_MODE_DISABLED,   // 禁用平均
            DL_ADC12_BURN_OUT_SOURCE_DISABLED,  // 禁用烧毁检测
            DL_ADC12_TRIGGER_MODE_AUTO_NEXT,    // 自动下一个
            DL_ADC12_WINDOWS_COMP_MODE_DISABLED // 禁用窗口比较
        );
    }

    // 设置采样时间
    DL_ADC12_setSampleTime0(ADC_VOLTAGE_INST, 40); // 10μs采样时间

    // 设置功耗模式
    DL_ADC12_setPowerDownMode(ADC_VOLTAGE_INST, DL_ADC12_POWER_DOWN_MODE_MANUAL);

    // 清除并使能中断
    DL_ADC12_clearInterruptStatus(ADC_VOLTAGE_INST, DL_ADC12_INTERRUPT_MEM0_RESULT_LOADED);
    DL_ADC12_enableInterrupt(ADC_VOLTAGE_INST, DL_ADC12_INTERRUPT_MEM0_RESULT_LOADED);

    // 使能ADC转换
    DL_ADC12_enableConversions(ADC_VOLTAGE_INST);

    return GRAYSCALE_SUCCESS;
}

/**
 * @brief 启动下一个通道的ADC转换
 * @param handle 系统句柄指针
 */
static void _GrayScale_StartNextConversion(GrayScale_Handle_t* handle) {
    if (!handle) return;

    // 配置当前通道
    uint8_t channel = handle->current_channel;

    // 重新配置ADC内存映射到当前通道
    DL_ADC12_configConversionMem(ADC_VOLTAGE_INST,
        DL_ADC12_MEM_IDX_0,                     // 使用MEM0
        (DL_ADC12_INPUT_CHAN_0 + channel),      // 当前输入通道
        DL_ADC12_REFERENCE_VOLTAGE_VDDA,        // 参考电压
        DL_ADC12_SAMPLE_TIMER_SOURCE_SCOMP0,    // 采样定时器
        DL_ADC12_AVERAGING_MODE_DISABLED,       // 禁用平均
        DL_ADC12_BURN_OUT_SOURCE_DISABLED,      // 禁用烧毁检测
        DL_ADC12_TRIGGER_MODE_AUTO_NEXT,        // 自动下一个
        DL_ADC12_WINDOWS_COMP_MODE_DISABLED     // 禁用窗口比较
    );

    // 启动ADC转换
    DL_ADC12_startConversion(ADC_VOLTAGE_INST);
}

/**
 * @brief 处理原始ADC数据
 * @param handle 系统句柄指针
 */
static void _GrayScale_ProcessRawData(GrayScale_Handle_t* handle) {
    if (!handle) return;

    // 获取当前通道的ADC结果
    uint8_t channel = handle->current_channel;
    uint16_t adc_value = DL_ADC12_getMemResult(ADC_VOLTAGE_INST, DL_ADC12_MEM_IDX_0);

    // 存储原始值
    handle->current_data.raw_values[channel] = adc_value;

    // 移动到下一个通道
    handle->current_channel++;

    // 检查是否完成一轮采样
    if (handle->current_channel >= GRAYSCALE_SENSOR_COUNT) {
        // 重置通道计数器
        handle->current_channel = 0;

        // 设置时间戳
        handle->current_data.timestamp = DL_Common_getTimerCount(GPTIMER_0_INST);

        // 标记转换完成
        handle->conversion_complete = true;

        // 应用数字滤波
        _GrayScale_ApplyDigitalFilter(handle);

        // 设置数据就绪标志
        handle->current_data.data_ready = true;
    } else {
        // 启动下一个通道的转换
        _GrayScale_StartNextConversion(handle);
    }
}

/**
 * @brief 应用数字滤波算法
 * @param handle 系统句柄指针
 */
static void _GrayScale_ApplyDigitalFilter(GrayScale_Handle_t* handle) {
    if (!handle) return;

    // 简单移动平均滤波 (这里简化为直接复制，实际应用中可以实现更复杂的滤波)
    for (int i = 0; i < GRAYSCALE_SENSOR_COUNT; i++) {
        // 当前实现：直接复制原始值
        // TODO: 实现移动平均滤波或其他滤波算法
        handle->current_data.filtered_values[i] = handle->current_data.raw_values[i];
    }
}

/**
 * @brief 计算线条位置
 * @param handle 系统句柄指针
 * @return 线条位置 (-3.0 ~ ****)
 */
static float _GrayScale_CalculateLinePosition(const GrayScale_Handle_t* handle) {
    if (!handle) return 0.0f;

    float weighted_sum = 0.0f;
    float total_weight = 0.0f;

    // 使用加权平均算法计算线条位置
    for (int i = 0; i < GRAYSCALE_SENSOR_COUNT; i++) {
        uint16_t sensor_value = handle->current_data.filtered_values[i];

        // 检查传感器是否检测到线条
        if (sensor_value > handle->config.detection_threshold) {
            float weight = (float)sensor_value / ADC_MAX_VALUE; // 归一化权重
            weighted_sum += handle->config.sensor_weights[i] * weight;
            total_weight += weight;
        }
    }

    // 计算加权平均位置
    if (total_weight > 0.0f) {
        return weighted_sum / total_weight;
    } else {
        // 没有检测到线条，返回中央位置
        return LINE_POSITION_CENTER;
    }
}

/**
 * @brief 更新PID控制器
 * @param handle 系统句柄指针
 * @param position_error 位置误差
 */
static void _GrayScale_UpdatePID(GrayScale_Handle_t* handle, float position_error) {
    if (!handle) return;

    // 计算导数项
    handle->line_result.derivative = position_error - handle->line_result.position_error;

    // 更新积分项 (简单积分，实际应用中需要防积分饱和)
    handle->line_result.integral += position_error;

    // 积分限幅
    const float integral_limit = 10.0f;
    if (handle->line_result.integral > integral_limit) {
        handle->line_result.integral = integral_limit;
    } else if (handle->line_result.integral < -integral_limit) {
        handle->line_result.integral = -integral_limit;
    }

    // 计算PID输出
    float pid_output = handle->config.pid_kp * position_error +
                      handle->config.pid_ki * handle->line_result.integral +
                      handle->config.pid_kd * handle->line_result.derivative;

    // 输出限幅
    const float output_limit = 100.0f;
    if (pid_output > output_limit) {
        pid_output = output_limit;
    } else if (pid_output < -output_limit) {
        pid_output = -output_limit;
    }

    handle->line_result.control_output = pid_output;
}

// ================================
// 中断处理函数
// ================================

/**
 * @brief ADC中断处理函数
 * @param handle 系统句柄指针
 */
void GrayScale_ADC_IRQHandler(GrayScale_Handle_t* handle) {
    if (!handle) return;

    // 检查中断源
    switch (DL_ADC12_getPendingInterrupt(ADC_VOLTAGE_INST)) {
        case DL_ADC12_IIDX_MEM0_RESULT_LOADED:
            // 处理ADC转换完成
            _GrayScale_ProcessRawData(handle);
            break;
        default:
            // 未知中断，增加错误计数
            handle->debug_info.error_count++;
            break;
    }
}

/**
 * @brief 全局ADC中断服务程序
 * 这个函数会被系统自动调用，需要替换原有的ADC中断处理函数
 */
void ADC0_IRQHandler(void) {
    if (g_grayscale_handle) {
        GrayScale_ADC_IRQHandler(g_grayscale_handle);
    }
}
