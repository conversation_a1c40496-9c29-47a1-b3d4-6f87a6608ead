/*
 * Copyright (c) 2025, MSPM0G3507 GrayScale Line Following Example
 * All rights reserved.
 *
 * 七路灰度循迹模块 - 应用示例
 * 展示如何使用灰度循迹API进行智能小车控制
 * 
 * 作者: Alex (工程师)
 * 版本: v1.0
 * 日期: 2025-07-10
 */

#include "ti_msp_dl_config.h"
#include "grayscale_line_following.h"
#include <stdio.h>
#include <math.h>

// ================================
// 应用配置常量
// ================================
#define MOTOR_PWM_FREQUENCY     1000    // 电机PWM频率 (Hz)
#define MOTOR_PWM_MAX_DUTY      100     // 最大占空比 (%)
#define CONTROL_LOOP_FREQ       50      // 控制循环频率 (Hz)
#define CALIBRATION_SAMPLES     100     // 校准采样次数

// ================================
// 电机控制结构
// ================================
typedef struct {
    float left_motor_speed;     // 左电机速度 (-100 ~ +100)
    float right_motor_speed;    // 右电机速度 (-100 ~ +100)
    float base_speed;           // 基础速度
    float max_turn_speed;       // 最大转向速度
} Motor_Control_t;

// ================================
// 全局变量
// ================================
static GrayScale_Handle_t g_grayscale_handle;
static Motor_Control_t g_motor_control;

// ================================
// 函数声明
// ================================
void LineFollowing_Example_Init(void);
void LineFollowing_Example_Run(void);
void LineFollowing_Calibration(void);
void LineFollowing_ControlLoop(void);
void Motor_SetSpeed(float left_speed, float right_speed);
void Motor_Stop(void);
void UART_SendString(const char* str);
void UART_Printf(const char* format, ...);

// ================================
// 主函数示例
// ================================
int line_following_main(void) {
    // 系统初始化
    SYSCFG_DL_init();
    
    UART_SendString("=== MSPM0G3507 智能循迹小车示例 ===\r\n");
    
    // 初始化循迹系统
    LineFollowing_Example_Init();
    
    // 校准传感器
    LineFollowing_Calibration();
    
    // 运行主循环
    LineFollowing_Example_Run();
    
    return 0;
}

// ================================
// 函数实现
// ================================

/**
 * @brief 初始化循迹示例系统
 */
void LineFollowing_Example_Init(void) {
    int result;
    
    // 初始化灰度循迹系统
    result = GrayScale_Init(&g_grayscale_handle);
    if (result != GRAYSCALE_SUCCESS) {
        UART_Printf("灰度系统初始化失败: %s\r\n", GrayScale_GetErrorString(result));
        while(1); // 停止执行
    }
    
    // 配置PID参数
    GrayScale_Config_t config;
    GrayScale_GetConfig(&g_grayscale_handle, &config);
    
    // 优化的PID参数 (根据实际测试调整)
    config.pid_kp = 2.0f;       // 比例系数
    config.pid_ki = 0.1f;       // 积分系数  
    config.pid_kd = 0.5f;       // 微分系数
    config.detection_threshold = 2000; // 检测阈值
    
    GrayScale_SetConfig(&g_grayscale_handle, &config);
    
    // 初始化电机控制参数
    g_motor_control.base_speed = 50.0f;      // 基础速度 50%
    g_motor_control.max_turn_speed = 80.0f;  // 最大转向速度 80%
    g_motor_control.left_motor_speed = 0.0f;
    g_motor_control.right_motor_speed = 0.0f;
    
    UART_SendString("循迹系统初始化完成\r\n");
}

/**
 * @brief 传感器校准过程
 */
void LineFollowing_Calibration(void) {
    UART_SendString("开始传感器校准...\r\n");
    UART_SendString("请将传感器放在白色背景上，按任意键继续\r\n");
    
    // 等待用户输入 (简化处理)
    delay_cycles(CPUCLK_FREQ * 3); // 等待3秒
    
    // 校准白线 (背景)
    int result = GrayScale_StartCalibration(&g_grayscale_handle);
    if (result == GRAYSCALE_SUCCESS) {
        result = GrayScale_CalibrateWhiteLine(&g_grayscale_handle);
    }
    
    UART_SendString("白线校准完成\r\n");
    UART_SendString("请将传感器放在黑线上，按任意键继续\r\n");
    
    delay_cycles(CPUCLK_FREQ * 3); // 等待3秒
    
    // 校准黑线
    if (result == GRAYSCALE_SUCCESS) {
        result = GrayScale_CalibrateBlackLine(&g_grayscale_handle);
        result = GrayScale_FinishCalibration(&g_grayscale_handle);
    }
    
    if (result == GRAYSCALE_SUCCESS) {
        UART_SendString("传感器校准成功！\r\n");
    } else {
        UART_Printf("传感器校准失败: %s\r\n", GrayScale_GetErrorString(result));
        while(1); // 停止执行
    }
}

/**
 * @brief 运行循迹主循环
 */
void LineFollowing_Example_Run(void) {
    int result;
    
    // 开始数据采样
    result = GrayScale_StartSampling(&g_grayscale_handle);
    if (result != GRAYSCALE_SUCCESS) {
        UART_Printf("启动采样失败: %s\r\n", GrayScale_GetErrorString(result));
        return;
    }
    
    UART_SendString("开始循迹控制...\r\n");
    
    uint32_t loop_counter = 0;
    uint32_t last_time = DL_Common_getTimerCount(GPTIMER_0_INST);
    
    // 主控制循环
    while (1) {
        uint32_t current_time = DL_Common_getTimerCount(GPTIMER_0_INST);
        
        // 控制循环频率 (50Hz)
        if ((current_time - last_time) >= (CPUCLK_FREQ / CONTROL_LOOP_FREQ)) {
            last_time = current_time;
            
            // 执行控制循环
            LineFollowing_ControlLoop();
            
            // 每秒输出一次调试信息
            loop_counter++;
            if (loop_counter >= CONTROL_LOOP_FREQ) {
                loop_counter = 0;
                
                // 输出系统状态
                GrayScale_Debug_Info_t debug_info;
                GrayScale_GetDebugInfo(&g_grayscale_handle, &debug_info);
                
                UART_Printf("状态: 采样=%lu, 错误=%lu, 处理时间=%lu μs\r\n",
                    debug_info.total_samples,
                    debug_info.error_count, 
                    debug_info.avg_processing_time
                );
            }
        }
    }
}

/**
 * @brief 循迹控制循环
 */
void LineFollowing_ControlLoop(void) {
    // 检查数据是否就绪
    if (!GrayScale_IsDataReady(&g_grayscale_handle)) {
        return;
    }
    
    // 处理循迹算法
    int result = GrayScale_ProcessLineFollowing(&g_grayscale_handle);
    if (result != GRAYSCALE_SUCCESS) {
        // 处理失败，停止电机
        Motor_Stop();
        return;
    }
    
    // 获取控制输出
    float control_output;
    result = GrayScale_GetControlOutput(&g_grayscale_handle, &control_output);
    if (result != GRAYSCALE_SUCCESS) {
        Motor_Stop();
        return;
    }
    
    // 检查是否检测到线条
    if (!g_grayscale_handle.line_result.line_detected) {
        // 没有检测到线条，停止或执行搜索策略
        Motor_Stop();
        return;
    }
    
    // 计算电机速度
    float left_speed = g_motor_control.base_speed - control_output;
    float right_speed = g_motor_control.base_speed + control_output;
    
    // 速度限幅
    if (left_speed > g_motor_control.max_turn_speed) {
        left_speed = g_motor_control.max_turn_speed;
    } else if (left_speed < -g_motor_control.max_turn_speed) {
        left_speed = -g_motor_control.max_turn_speed;
    }
    
    if (right_speed > g_motor_control.max_turn_speed) {
        right_speed = g_motor_control.max_turn_speed;
    } else if (right_speed < -g_motor_control.max_turn_speed) {
        right_speed = -g_motor_control.max_turn_speed;
    }
    
    // 设置电机速度
    Motor_SetSpeed(left_speed, right_speed);
    
    // 更新电机控制状态
    g_motor_control.left_motor_speed = left_speed;
    g_motor_control.right_motor_speed = right_speed;
}

/**
 * @brief 设置电机速度
 * @param left_speed 左电机速度 (-100 ~ +100)
 * @param right_speed 右电机速度 (-100 ~ +100)
 */
void Motor_SetSpeed(float left_speed, float right_speed) {
    // 这里应该实现实际的电机PWM控制
    // 由于这是示例代码，只输出调试信息
    
    // TODO: 实现实际的PWM输出控制
    // 例如: 
    // DL_Timer_setPWMDutyCycle(LEFT_MOTOR_TIMER, left_speed);
    // DL_Timer_setPWMDutyCycle(RIGHT_MOTOR_TIMER, right_speed);
}

/**
 * @brief 停止电机
 */
void Motor_Stop(void) {
    Motor_SetSpeed(0.0f, 0.0f);
}

/**
 * @brief UART发送字符串
 * @param str 要发送的字符串
 */
void UART_SendString(const char* str) {
    while(*str) {
        DL_UART_transmitDataBlocking(UART_0_INST, *str++);
    }
}

/**
 * @brief UART格式化输出
 * @param format 格式字符串
 * @param ... 可变参数
 */
void UART_Printf(const char* format, ...) {
    char buffer[256];
    va_list args;
    va_start(args, format);
    vsnprintf(buffer, sizeof(buffer), format, args);
    va_end(args);
    UART_SendString(buffer);
}
