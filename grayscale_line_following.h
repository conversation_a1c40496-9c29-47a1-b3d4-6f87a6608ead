/*
 * Copyright (c) 2025, MSPM0G3507 GrayScale Line Following Module
 * All rights reserved.
 *
 * 七路灰度循迹模块 - 头文件定义
 * 基于MSPM0G3507嘉立创天猛星开发板
 * 
 * 作者: Alex (工程师)
 * 版本: v1.0
 * 日期: 2025-07-10
 */

#ifndef GRAYSCALE_LINE_FOLLOWING_H
#define GRAYSCALE_LINE_FOLLOWING_H

#include "ti_msp_dl_config.h"
#include <stdint.h>
#include <stdbool.h>
#include <string.h>

#ifdef __cplusplus
extern "C" {
#endif

// ================================
// 系统配置常量定义
// ================================
#define GRAYSCALE_SENSOR_COUNT          7       // 传感器数量
#define GRAYSCALE_SAMPLING_FREQ         1000    // 采样频率 (Hz)
#define GRAYSCALE_FILTER_WINDOW_SIZE    4       // 滤波窗口大小
#define GRAYSCALE_BUFFER_SIZE           16      // 数据缓冲区大小

// ADC相关常量
#define ADC_MAX_VALUE                   4095    // 12位ADC最大值
#define ADC_REFERENCE_VOLTAGE           3.3f    // 参考电压 (V)
#define ADC_CONVERSION_TIMEOUT          1000    // 转换超时 (μs)

// 循迹算法常量
#define LINE_POSITION_MIN               -3.0f   // 最左侧位置
#define LINE_POSITION_MAX               3.0f    // 最右侧位置
#define LINE_POSITION_CENTER            0.0f    // 中央位置
#define DETECTION_THRESHOLD_DEFAULT     2000    // 默认检测阈值

// ================================
// 错误代码定义
// ================================
typedef enum {
    GRAYSCALE_SUCCESS = 0,              // 成功
    GRAYSCALE_ERROR_PARAM = -1,         // 参数错误
    GRAYSCALE_ERROR_HARDWARE = -2,      // 硬件错误
    GRAYSCALE_ERROR_TIMEOUT = -3,       // 超时错误
    GRAYSCALE_ERROR_BUSY = -4,          // 系统忙
    GRAYSCALE_ERROR_NOT_INITIALIZED = -5, // 未初始化
    GRAYSCALE_ERROR_CALIBRATION = -6    // 校准错误
} GrayScale_Error_t;

// ================================
// 传感器ID枚举
// ================================
typedef enum {
    GRAYSCALE_SENSOR_0 = 0,  // PA27 -> ADC0_CH0 (最左侧)
    GRAYSCALE_SENSOR_1 = 1,  // PA26 -> ADC0_CH1 (左侧)
    GRAYSCALE_SENSOR_2 = 2,  // PA25 -> ADC0_CH2 (左中)
    GRAYSCALE_SENSOR_3 = 3,  // PA24 -> ADC0_CH3 (中央)
    GRAYSCALE_SENSOR_4 = 4,  // PA23 -> ADC0_CH4 (右中)
    GRAYSCALE_SENSOR_5 = 5,  // PA22 -> ADC0_CH5 (右侧)
    GRAYSCALE_SENSOR_6 = 6   // PA21 -> ADC0_CH6 (最右侧)
} GrayScale_Sensor_ID_t;

// ================================
// 系统状态枚举
// ================================
typedef enum {
    GRAYSCALE_STATE_UNINITIALIZED = 0,  // 未初始化
    GRAYSCALE_STATE_INITIALIZED,        // 已初始化
    GRAYSCALE_STATE_CALIBRATING,        // 校准中
    GRAYSCALE_STATE_RUNNING,            // 运行中
    GRAYSCALE_STATE_ERROR               // 错误状态
} GrayScale_State_t;

// ================================
// 核心数据结构定义
// ================================

// 原始传感器数据结构
typedef struct {
    uint16_t raw_values[GRAYSCALE_SENSOR_COUNT];      // 原始ADC值
    uint16_t filtered_values[GRAYSCALE_SENSOR_COUNT]; // 滤波后的值
    uint32_t timestamp;                               // 采样时间戳
    bool data_ready;                                  // 数据就绪标志
    uint8_t error_flags;                              // 错误标志位
} GrayScale_Data_t;

// 校准数据结构
typedef struct {
    uint16_t min_values[GRAYSCALE_SENSOR_COUNT];      // 最小值 (白线)
    uint16_t max_values[GRAYSCALE_SENSOR_COUNT];      // 最大值 (黑线)
    uint16_t threshold_values[GRAYSCALE_SENSOR_COUNT]; // 阈值
    bool calibrated;                                  // 校准完成标志
} GrayScale_Calibration_t;

// 循迹算法结果结构
typedef struct {
    float line_position;        // 线条位置 (-3.0 ~ +3.0)
    float position_error;       // 位置误差
    float derivative;           // 误差导数
    float integral;             // 误差积分
    float control_output;       // 控制输出
    bool line_detected;         // 线条检测状态
    uint8_t active_sensors;     // 激活的传感器数量
} LineFollowing_Result_t;

// 系统配置结构
typedef struct {
    uint16_t sampling_frequency;                      // 采样频率 (Hz)
    uint8_t filter_window_size;                       // 滤波窗口大小
    uint16_t detection_threshold;                     // 检测阈值
    float sensor_weights[GRAYSCALE_SENSOR_COUNT];     // 传感器权重
    float pid_kp, pid_ki, pid_kd;                     // PID参数
    bool auto_calibration;                            // 自动校准使能
} GrayScale_Config_t;

// 性能监控结构
typedef struct {
    uint32_t total_samples;         // 总采样次数
    uint32_t error_count;           // 错误计数
    uint32_t max_processing_time;   // 最大处理时间 (μs)
    uint32_t avg_processing_time;   // 平均处理时间 (μs)
    uint16_t cpu_usage_percent;     // CPU使用率 (%)
    uint8_t memory_usage_kb;        // 内存使用量 (KB)
} GrayScale_Debug_Info_t;

// 主控制结构
typedef struct {
    GrayScale_State_t state;                          // 系统状态
    GrayScale_Config_t config;                        // 配置参数
    GrayScale_Data_t current_data;                    // 当前数据
    GrayScale_Calibration_t calibration;              // 校准数据
    LineFollowing_Result_t line_result;               // 循迹结果
    GrayScale_Debug_Info_t debug_info;                // 调试信息
    uint8_t current_channel;                          // 当前采样通道
    bool conversion_complete;                         // 转换完成标志
} GrayScale_Handle_t;

// ================================
// 核心API函数声明
// ================================

// 系统初始化和配置
int GrayScale_Init(GrayScale_Handle_t* handle);
int GrayScale_DeInit(GrayScale_Handle_t* handle);
int GrayScale_SetConfig(GrayScale_Handle_t* handle, const GrayScale_Config_t* config);
int GrayScale_GetConfig(GrayScale_Handle_t* handle, GrayScale_Config_t* config);

// 数据采集和处理
int GrayScale_StartSampling(GrayScale_Handle_t* handle);
int GrayScale_StopSampling(GrayScale_Handle_t* handle);
int GrayScale_GetData(GrayScale_Handle_t* handle, GrayScale_Data_t* data);
bool GrayScale_IsDataReady(GrayScale_Handle_t* handle);

// 校准功能
int GrayScale_StartCalibration(GrayScale_Handle_t* handle);
int GrayScale_CalibrateWhiteLine(GrayScale_Handle_t* handle);
int GrayScale_CalibrateBlackLine(GrayScale_Handle_t* handle);
int GrayScale_FinishCalibration(GrayScale_Handle_t* handle);

// 循迹算法
int GrayScale_ProcessLineFollowing(GrayScale_Handle_t* handle);
int GrayScale_GetLinePosition(GrayScale_Handle_t* handle, float* position);
int GrayScale_GetControlOutput(GrayScale_Handle_t* handle, float* output);

// 调试和监控
int GrayScale_GetDebugInfo(GrayScale_Handle_t* handle, GrayScale_Debug_Info_t* info);
int GrayScale_ResetDebugInfo(GrayScale_Handle_t* handle);
const char* GrayScale_GetErrorString(GrayScale_Error_t error);

// 中断处理函数 (内部使用)
void GrayScale_ADC_IRQHandler(GrayScale_Handle_t* handle);

// ================================
// 内联辅助函数
// ================================

// 获取系统状态
static inline GrayScale_State_t GrayScale_GetState(const GrayScale_Handle_t* handle) {
    return handle ? handle->state : GRAYSCALE_STATE_UNINITIALIZED;
}

// 检查是否已初始化
static inline bool GrayScale_IsInitialized(const GrayScale_Handle_t* handle) {
    return handle && (handle->state != GRAYSCALE_STATE_UNINITIALIZED);
}

// 检查是否正在运行
static inline bool GrayScale_IsRunning(const GrayScale_Handle_t* handle) {
    return handle && (handle->state == GRAYSCALE_STATE_RUNNING);
}

// 获取传感器数量
static inline uint8_t GrayScale_GetSensorCount(void) {
    return GRAYSCALE_SENSOR_COUNT;
}

#ifdef __cplusplus
}
#endif

#endif // GRAYSCALE_LINE_FOLLOWING_H
