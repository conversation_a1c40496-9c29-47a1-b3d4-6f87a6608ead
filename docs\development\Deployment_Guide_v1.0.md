# 部署指南: MSPM0G3507七路灰度循迹模块

## 1. 文档信息

| 项目信息 | 详情 |
|---------|------|
| **模块名称** | MSPM0G3507七路灰度循迹模块部署指南 |
| **版本号** | v1.0 |
| **创建日期** | 2025-07-10 |
| **作者** | Alex (工程师) |
| **适用平台** | MSPM0G3507嘉立创天猛星开发板 |

## 2. 系统要求

### 2.1 硬件要求
- **开发板**: MSPM0G3507嘉立创天猛星开发板
- **传感器**: 7路反射式红外灰度传感器模块
- **连接线**: 杜邦线或专用连接线
- **电源**: USB供电或外部3.3V电源
- **调试器**: 板载XDS-110调试器

### 2.2 软件要求
- **IDE**: Code Composer Studio 12.0+ 或 IAR Embedded Workbench
- **SDK**: MSPM0 SDK 2.01.00.03+
- **编译器**: TI Clang或IAR C/C++编译器
- **调试工具**: 串口调试助手 (9600波特率)

## 3. 硬件连接指南

### 3.1 传感器模块连接

#### 标准7路灰度传感器模块
```
传感器模块    →    MSPM0G3507开发板
VCC          →    3.3V (J3_1)
GND          →    GND (J3_2)
S0 (最左)    →    PA27 (J1_2)
S1 (左)      →    PA26 (J1_5)
S2 (左中)    →    PA25 (J1_6)
S3 (中央)    →    PA24 (J1_7)
S4 (右中)    →    PA23 (J1_8)
S5 (右)      →    PA22 (J1_9)
S6 (最右)    →    PA21 (J1_10)
```

#### 连接注意事项
- ⚠️ 确保电源电压为3.3V，避免5V供电损坏MCU
- ⚠️ 信号线连接要牢固，避免接触不良
- ⚠️ 传感器模块要水平安装，距离地面2-5mm
- ⚠️ 7个传感器要在同一水平线上，间距均匀

### 3.2 调试接口连接
```
功能          引脚      连接说明
UART调试      PA10/PA11  连接USB转串口模块 (可选)
SWD调试       PA19/PA20  板载调试器，无需外接
电源指示      LED1       板载LED，显示电源状态
```

## 4. 软件部署步骤

### 4.1 使用Code Composer Studio

#### 步骤1: 环境准备
1. 安装Code Composer Studio 12.0+
2. 安装MSPM0 SDK 2.01.00.03+
3. 连接开发板到PC (USB线)

#### 步骤2: 导入项目
```bash
1. 打开CCS
2. File → Import → CCS Projects
3. 选择项目目录
4. 勾选 "Copy projects into workspace"
5. 点击 "Finish"
```

#### 步骤3: 编译项目
```bash
1. 右键项目 → Build Project
2. 或使用快捷键 Ctrl+B
3. 检查编译输出，确保无错误
```

#### 步骤4: 烧录和调试
```bash
1. 右键项目 → Debug As → CCS Debug
2. 或使用快捷键 F11
3. 程序自动烧录并启动调试
```

### 4.2 使用命令行编译

#### 准备环境
```bash
# 设置环境变量 (Windows)
set MSPM0_SDK_INSTALL_DIR=C:\ti\mspm0_sdk_2_01_00_03
set CCS_INSTALL_DIR=C:\ti\ccs1280

# 设置环境变量 (Linux)
export MSPM0_SDK_INSTALL_DIR=/opt/ti/mspm0_sdk_2_01_00_03
export CCS_INSTALL_DIR=/opt/ti/ccs1280
```

#### 编译命令
```bash
# 进入编译目录
cd ticlang

# 清理项目
make clean

# 编译项目
make all

# 检查生成的文件
ls -la *.out *.map
```

### 4.3 烧录程序

#### 使用CCS烧录
```bash
1. 在CCS中右键项目
2. 选择 "Load Program"
3. 选择生成的.out文件
4. 点击 "Load" 开始烧录
```

#### 使用命令行烧录
```bash
# 使用uniflash工具
uniflash.sh -ccxml MSPM0G3507.ccxml -program empty.out
```

## 5. 系统配置

### 5.1 传感器校准

#### 自动校准流程
```c
// 1. 初始化系统
GrayScale_Handle_t handle;
GrayScale_Init(&handle);

// 2. 开始校准
GrayScale_StartCalibration(&handle);

// 3. 校准白线 (背景)
// 将传感器放在白色背景上
GrayScale_CalibrateWhiteLine(&handle);

// 4. 校准黑线
// 将传感器放在黑线上  
GrayScale_CalibrateBlackLine(&handle);

// 5. 完成校准
GrayScale_FinishCalibration(&handle);
```

#### 手动校准参数
```c
// 获取当前配置
GrayScale_Config_t config;
GrayScale_GetConfig(&handle, &config);

// 设置检测阈值 (根据环境调整)
config.detection_threshold = 2000;  // 典型值: 1500-2500

// 设置PID参数
config.pid_kp = 2.0f;   // 比例系数
config.pid_ki = 0.1f;   // 积分系数
config.pid_kd = 0.5f;   // 微分系数

// 应用配置
GrayScale_SetConfig(&handle, &config);
```

### 5.2 性能优化配置

#### 采样频率调整
```c
// 高精度模式 (1kHz)
config.sampling_frequency = 1000;

// 高速模式 (2kHz, 可能影响稳定性)
config.sampling_frequency = 2000;

// 节能模式 (500Hz)
config.sampling_frequency = 500;
```

#### 滤波参数调整
```c
// 滤波窗口大小 (1-8)
config.filter_window_size = 4;  // 默认值，平衡响应速度和稳定性
```

## 6. 测试验证

### 6.1 功能测试

#### 基本功能测试
```c
// 测试1: 系统初始化
int result = GrayScale_Init(&handle);
assert(result == GRAYSCALE_SUCCESS);

// 测试2: 数据采集
GrayScale_StartSampling(&handle);
delay_ms(100);
assert(GrayScale_IsDataReady(&handle));

// 测试3: 循迹算法
result = GrayScale_ProcessLineFollowing(&handle);
assert(result == GRAYSCALE_SUCCESS);
```

#### 性能测试
```c
// 测试采样频率
uint32_t start_time = get_system_time();
uint32_t sample_count = 0;

while (get_system_time() - start_time < 1000) { // 1秒测试
    if (GrayScale_IsDataReady(&handle)) {
        sample_count++;
        handle.current_data.data_ready = false;
    }
}

printf("实际采样频率: %lu Hz\n", sample_count);
```

### 6.2 调试输出

#### 串口调试信息
```c
// 启用详细调试输出
#define DEBUG_VERBOSE 1

// 输出传感器原始数据
void debug_print_sensor_data(GrayScale_Handle_t* handle) {
    printf("传感器数据: ");
    for (int i = 0; i < 7; i++) {
        printf("[%d]=%d ", i, handle->current_data.raw_values[i]);
    }
    printf("\n");
}

// 输出循迹结果
void debug_print_line_result(GrayScale_Handle_t* handle) {
    printf("位置:%.2f, 控制:%.1f, 检测:%s\n",
        handle->line_result.line_position,
        handle->line_result.control_output,
        handle->line_result.line_detected ? "是" : "否"
    );
}
```

## 7. 故障排除

### 7.1 常见问题

#### 编译错误
```
错误: undefined reference to 'GrayScale_Init'
解决: 确保grayscale_line_following.c已添加到项目中

错误: 'DL_ADC12_MEM_IDX_1' undeclared
解决: 检查ti_msp_dl_config.h中的ADC通道定义

错误: multiple definition of 'ADC0_IRQHandler'
解决: 删除原有的ADC中断处理函数，使用模块提供的版本
```

#### 运行时错误
```
问题: 传感器读数全为0
检查: GPIO引脚配置，传感器供电，连接线

问题: 系统无响应
检查: 中断配置，时钟配置，看门狗设置

问题: 循迹效果差
调整: PID参数，检测阈值，传感器安装位置
```

### 7.2 调试技巧

#### 使用CCS调试器
```c
// 设置断点
1. 在关键函数入口设置断点
2. 检查变量值和程序流程
3. 使用Watch窗口监控关键变量

// 实时监控
1. 使用Expressions窗口监控传感器数据
2. 使用Memory Browser查看内存使用
3. 使用Registers窗口检查ADC寄存器状态
```

#### 性能分析
```c
// CPU使用率监控
uint32_t idle_count = 0;
uint32_t total_count = 0;

while (1) {
    total_count++;
    if (!system_busy()) {
        idle_count++;
    }
    
    // 每秒输出一次
    if (total_count % 100000 == 0) {
        float cpu_usage = (1.0f - (float)idle_count/total_count) * 100;
        printf("CPU使用率: %.1f%%\n", cpu_usage);
    }
}
```

## 8. 维护和升级

### 8.1 定期维护
- 🔧 每月检查传感器清洁度，清除灰尘
- 🔧 每季度重新校准传感器
- 🔧 定期检查连接线是否松动
- 🔧 监控系统性能指标变化

### 8.2 版本升级
- 📦 备份当前配置参数
- 📦 测试新版本兼容性
- 📦 逐步部署，先测试后生产
- 📦 保留回滚方案

---

**部署指南状态**: ✅ 已完成  
**最后更新**: 2025-07-10  
**版本**: v1.0
