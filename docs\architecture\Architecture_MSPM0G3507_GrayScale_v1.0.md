# 系统架构设计: MSPM0G3507七路灰度循迹模块

## 1. 架构文档信息

| 项目信息 | 详情 |
|---------|------|
| **项目名称** | MSPM0G3507七路灰度循迹模块架构设计 |
| **版本号** | v1.0 |
| **创建日期** | 2025-07-10 |
| **架构师** | Bob (系统架构师) |
| **审核状态** | 设计阶段 |
| **目标平台** | MSPM0G3507 (ARM Cortex-M0+) |

## 2. 架构概览

### 2.1 系统架构图
```
┌─────────────────────────────────────────────────────────────┐
│                    应用层 (Application Layer)                │
├─────────────────────────────────────────────────────────────┤
│  循迹控制算法  │  参数配置  │  调试接口  │  状态监控        │
├─────────────────────────────────────────────────────────────┤
│                    算法层 (Algorithm Layer)                  │
├─────────────────────────────────────────────────────────────┤
│  数据滤波     │  线条检测  │  位置计算  │  偏差算法        │
├─────────────────────────────────────────────────────────────┤
│                    驱动层 (Driver Layer)                     │
├─────────────────────────────────────────────────────────────┤
│  多通道ADC驱动 │  GPIO配置  │  中断管理  │  数据缓冲        │
├─────────────────────────────────────────────────────────────┤
│                    硬件抽象层 (HAL Layer)                    │
├─────────────────────────────────────────────────────────────┤
│  TI DriverLib  │  MSPM0 SDK │  系统配置  │  时钟管理        │
└─────────────────────────────────────────────────────────────┘
```

### 2.2 核心设计原则
1. **模块化设计**: 各层职责清晰，接口标准化
2. **实时性保证**: 中断驱动 + 高效算法，确保≤5ms响应
3. **资源优化**: 最小化内存使用，CPU占用≤30%
4. **可扩展性**: 支持参数配置和功能扩展
5. **容错设计**: 异常检测和恢复机制

## 3. 硬件架构设计

### 3.1 ADC资源分配策略

#### 3.1.1 多通道ADC配置方案
```c
// ADC通道映射表
typedef enum {
    GRAYSCALE_SENSOR_0 = 0,  // PA27 -> ADC0_CH0 (最左)
    GRAYSCALE_SENSOR_1 = 1,  // PA26 -> ADC0_CH1 (左)
    GRAYSCALE_SENSOR_2 = 2,  // PA25 -> ADC0_CH2 (左中)
    GRAYSCALE_SENSOR_3 = 3,  // PA24 -> ADC0_CH3 (中央)
    GRAYSCALE_SENSOR_4 = 4,  // PA23 -> ADC0_CH4 (右中)
    GRAYSCALE_SENSOR_5 = 5,  // PA22 -> ADC0_CH5 (右)
    GRAYSCALE_SENSOR_6 = 6,  // PA21 -> ADC0_CH6 (最右)
    GRAYSCALE_SENSOR_COUNT = 7
} GrayScale_Sensor_ID_t;
```

#### 3.1.2 GPIO引脚复用配置
| GPIO引脚 | ADC通道 | IOMUX配置 | 功能描述 |
|----------|---------|-----------|----------|
| PA27 | ADC0_CH0 | IOMUX_PINCM28 | 传感器0模拟输入 |
| PA26 | ADC0_CH1 | IOMUX_PINCM27 | 传感器1模拟输入 |
| PA25 | ADC0_CH2 | IOMUX_PINCM26 | 传感器2模拟输入 |
| PA24 | ADC0_CH3 | IOMUX_PINCM25 | 传感器3模拟输入 |
| PA23 | ADC0_CH4 | IOMUX_PINCM24 | 传感器4模拟输入 |
| PA22 | ADC0_CH5 | IOMUX_PINCM23 | 传感器5模拟输入 |
| PA21 | ADC0_CH6 | IOMUX_PINCM22 | 传感器6模拟输入 |

### 3.2 采样时序架构

#### 3.2.1 顺序采样模式 (推荐方案)
```
时间轴: 0ms    1ms    2ms    3ms    4ms    5ms    6ms    7ms
       ┌──────┬──────┬──────┬──────┬──────┬──────┬──────┬──────
CH0:   │ ADC  │      │      │      │      │      │      │ ADC
CH1:   │      │ ADC  │      │      │      │      │      │
CH2:   │      │      │ ADC  │      │      │      │      │
CH3:   │      │      │      │ ADC  │      │      │      │
CH4:   │      │      │      │      │ ADC  │      │      │
CH5:   │      │      │      │      │      │ ADC  │      │
CH6:   │      │      │      │      │      │      │ ADC  │
       └──────┴──────┴──────┴──────┴──────┴──────┴──────┴──────
采样频率: 1kHz (每通道), 7kHz (总采样率)
```

#### 3.2.2 采样参数配置
- **采样时钟**: SYSOSC/8 = 4MHz
- **转换时间**: ~3μs (12位ADC)
- **采样时间**: 可配置 (默认10μs)
- **总延迟**: <100μs (7通道顺序采样)

## 4. 软件架构设计

### 4.1 分层架构详述

#### 4.1.1 硬件抽象层 (HAL)
```c
// HAL层接口定义
typedef struct {
    void (*init)(void);
    void (*deinit)(void);
    int (*start_conversion)(uint8_t channel);
    uint16_t (*get_result)(uint8_t channel);
    bool (*is_ready)(uint8_t channel);
} ADC_HAL_Interface_t;
```

#### 4.1.2 驱动层 (Driver Layer)
```c
// 多通道ADC驱动核心结构
typedef struct {
    uint16_t raw_values[7];      // 原始ADC值
    uint16_t filtered_values[7]; // 滤波后的值
    uint32_t timestamp;          // 采样时间戳
    uint8_t current_channel;     // 当前采样通道
    bool conversion_complete;    // 转换完成标志
    uint8_t error_flags;         // 错误标志位
} GrayScale_Driver_Data_t;

// 驱动层API
int GrayScale_Driver_Init(void);
int GrayScale_Driver_StartSampling(void);
int GrayScale_Driver_GetData(GrayScale_Driver_Data_t* data);
int GrayScale_Driver_Calibrate(void);
```

#### 4.1.3 算法层 (Algorithm Layer)
```c
// 循迹算法核心结构
typedef struct {
    float sensor_weights[7];     // 传感器权重系数
    float line_position;         // 线条位置 (-3.0 ~ +3.0)
    float position_error;        // 位置误差
    float derivative;            // 误差导数
    float integral;              // 误差积分
    bool line_detected;          // 线条检测状态
} LineFollowing_Algorithm_t;

// 算法层API
int LineFollowing_Init(LineFollowing_Algorithm_t* algo);
int LineFollowing_Process(GrayScale_Driver_Data_t* sensor_data, 
                         LineFollowing_Algorithm_t* result);
float LineFollowing_GetControlOutput(LineFollowing_Algorithm_t* algo);
```

### 4.2 中断处理架构

#### 4.2.1 中断优先级配置
```c
// 中断优先级定义
#define ADC_IRQ_PRIORITY        0  // 最高优先级
#define TIMER_IRQ_PRIORITY      1  // 定时器中断
#define UART_IRQ_PRIORITY       2  // 串口中断
```

#### 4.2.2 中断服务流程
```
ADC中断触发 → 读取转换结果 → 切换到下一通道 → 启动下一次转换
     ↓
检查是否完成一轮采样 → 设置数据就绪标志 → 唤醒主任务
     ↓
主任务处理数据 → 执行滤波算法 → 计算线条位置 → 输出控制信号
```

### 4.3 数据流架构

#### 4.3.1 数据处理管道
```
原始ADC值 → 数字滤波 → 阈值处理 → 线条检测 → 位置计算 → 控制输出
    ↓           ↓          ↓          ↓          ↓          ↓
  12位数据   移动平均   二值化处理   加权平均   PID控制   PWM输出
```

#### 4.3.2 数据缓冲管理
```c
// 环形缓冲区设计
#define BUFFER_SIZE 16
typedef struct {
    GrayScale_Driver_Data_t buffer[BUFFER_SIZE];
    uint8_t write_index;
    uint8_t read_index;
    uint8_t count;
    bool overflow;
} GrayScale_Buffer_t;
```

## 5. 性能优化策略

### 5.1 实时性优化
1. **中断驱动**: 避免轮询，减少CPU占用
2. **DMA传输**: 考虑使用DMA减少中断开销
3. **算法优化**: 使用定点运算替代浮点运算
4. **内存对齐**: 优化数据结构内存布局

### 5.2 资源使用优化
```c
// 内存使用估算
#define DRIVER_DATA_SIZE    sizeof(GrayScale_Driver_Data_t)    // ~32 bytes
#define ALGORITHM_DATA_SIZE sizeof(LineFollowing_Algorithm_t)  // ~64 bytes
#define BUFFER_SIZE_TOTAL   (BUFFER_SIZE * DRIVER_DATA_SIZE)   // ~512 bytes
// 总内存使用: ~608 bytes (< 1KB)
```

### 5.3 功耗优化
1. **动态时钟管理**: 根据采样频率调整时钟
2. **低功耗模式**: 空闲时进入SLEEP模式
3. **传感器电源管理**: 可选的传感器电源控制

## 6. 错误处理与容错设计

### 6.1 硬件故障检测
```c
// 错误类型定义
typedef enum {
    GRAYSCALE_ERROR_NONE = 0x00,
    GRAYSCALE_ERROR_ADC_TIMEOUT = 0x01,
    GRAYSCALE_ERROR_SENSOR_DISCONNECT = 0x02,
    GRAYSCALE_ERROR_POWER_FAILURE = 0x04,
    GRAYSCALE_ERROR_CALIBRATION_FAIL = 0x08
} GrayScale_Error_t;
```

### 6.2 软件异常处理
1. **看门狗保护**: 防止系统死锁
2. **数据有效性检查**: ADC值范围验证
3. **自动恢复机制**: 错误后自动重新初始化
4. **降级运行**: 部分传感器故障时的备用方案

## 7. 接口设计规范

### 7.1 API接口标准
```c
// 返回值标准
#define GRAYSCALE_SUCCESS           0
#define GRAYSCALE_ERROR_PARAM      -1
#define GRAYSCALE_ERROR_HARDWARE   -2
#define GRAYSCALE_ERROR_TIMEOUT    -3
#define GRAYSCALE_ERROR_BUSY       -4

// 配置参数结构
typedef struct {
    uint16_t sampling_frequency;    // 采样频率 (Hz)
    uint8_t filter_window_size;     // 滤波窗口大小
    uint16_t detection_threshold;   // 检测阈值
    float pid_kp, pid_ki, pid_kd;   // PID参数
} GrayScale_Config_t;
```

### 7.2 调试接口设计
```c
// 调试信息结构
typedef struct {
    uint32_t total_samples;         // 总采样次数
    uint32_t error_count;           // 错误计数
    uint16_t min_values[7];         // 最小值记录
    uint16_t max_values[7];         // 最大值记录
    float average_processing_time;  // 平均处理时间
} GrayScale_Debug_Info_t;
```

## 8. 技术选型决策

### 8.1 关键技术选择

| 技术选择 | 方案A | 方案B | 选择结果 | 理由 |
|---------|-------|-------|----------|------|
| **采样模式** | 顺序采样 | 并行采样 | ✅ 顺序采样 | 硬件资源限制，实现简单 |
| **数据传输** | 中断驱动 | DMA传输 | ✅ 中断驱动 | 数据量小，中断开销可接受 |
| **滤波算法** | 移动平均 | 卡尔曼滤波 | ✅ 移动平均 | 计算简单，实时性好 |
| **数值类型** | 浮点运算 | 定点运算 | ✅ 定点运算 | M0+无FPU，定点更高效 |

### 8.2 架构决策记录 (ADR)

#### ADR-001: 采用顺序采样而非并行采样
- **状态**: 已接受
- **决策**: 使用单个ADC模块的多通道顺序采样
- **理由**: MSPM0G3507只有一个ADC12模块，无法真正并行采样
- **后果**: 总采样时间增加，但仍能满足1kHz要求

#### ADR-002: 使用中断驱动而非轮询模式
- **状态**: 已接受
- **决策**: ADC转换完成后触发中断处理
- **理由**: 提高CPU利用率，支持低功耗模式
- **后果**: 增加中断处理复杂度，但性能收益明显

## 9. 部署与集成方案

### 9.1 编译配置
```makefile
# 编译优化选项
CFLAGS += -O2 -ffast-math -fno-strict-aliasing
CFLAGS += -DGRAYSCALE_SENSOR_COUNT=7
CFLAGS += -DGRAYSCALE_SAMPLING_FREQ=1000
```

### 9.2 内存映射
```
Flash Memory Layout:
0x00000000 - 0x00001FFF: Bootloader (8KB)
0x00002000 - 0x0001FFFF: Application Code (120KB)

RAM Memory Layout:
0x20200000 - 0x20200400: System Stack (1KB)
0x20200400 - 0x20200800: Application Data (1KB)
0x20200800 - 0x20207FFF: Available RAM (29KB)
```

## 10. 测试与验证策略

### 10.1 单元测试计划
- ADC驱动功能测试
- 数据滤波算法测试
- 线条检测算法测试
- 错误处理机制测试

### 10.2 集成测试计划
- 多通道采样同步性测试
- 实时性能测试
- 长时间稳定性测试
- 异常恢复测试

### 10.3 性能基准测试
- 采样频率达标测试: ≥1kHz
- 响应时间测试: ≤5ms
- CPU占用率测试: ≤30%
- 内存使用测试: ≤1KB

---

**架构设计状态**: ✅ 已完成  
**下一步**: 开始详细设计和代码实现  
**风险评估**: 🟢 低风险，技术方案可行
