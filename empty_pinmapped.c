/*
 * MSPM0G3507 七路灰度循迹模块 - 引脚适配版本
 * 根据您的具体引脚配置: B12,B17,B04,B01,A28,A31,B15
 * 
 * 作者: Alex (工程师)
 * 版本: v1.0-pinmapped
 * 日期: 2025-07-10
 */

#include "ti_msp_dl_config.h"
#include <stdio.h>
#include <stdbool.h>
#include <stdint.h>

// ================================
// 延时宏定义
// ================================
#define delay_ms(X)	DL_Common_delayCycles((CPUCLK_FREQ/1000)*(X))

// ================================
// 灰度传感器常量定义
// ================================
#define GRAYSCALE_SENSOR_COUNT          7       // 传感器数量
#define ADC_MAX_VALUE                   4095    // 12位ADC最大值
#define DETECTION_THRESHOLD             2000    // 检测阈值

// ================================
// 您的引脚配置定义
// ================================
// 传感器引脚映射 (按您提供的顺序)
// 传感器1-4,7: 数字GPIO引脚
// 传感器5,6: 模拟ADC引脚

// GPIO引脚定义 (简化版本，避免复杂配置)
typedef struct {
    uint8_t channel;        // 传感器通道号
    bool is_adc;           // 是否为ADC引脚
    uint32_t gpio_port;    // GPIO端口 (如果是GPIO)
    uint32_t gpio_pin;     // GPIO引脚 (如果是GPIO)
} sensor_pin_config_t;

// 您的引脚配置表
static const sensor_pin_config_t pin_config[GRAYSCALE_SENSOR_COUNT] = {
    {0, false, (uint32_t)GPIOB_BASE, DL_GPIO_PIN_12},  // 传感器1: B12
    {1, false, (uint32_t)GPIOB_BASE, DL_GPIO_PIN_17},  // 传感器2: B17  
    {2, false, (uint32_t)GPIOB_BASE, DL_GPIO_PIN_4},   // 传感器3: B04
    {3, false, (uint32_t)GPIOB_BASE, DL_GPIO_PIN_1},   // 传感器4: B01
    {4, true,  0, 0},                                  // 传感器5: A28 (ADC)
    {5, true,  0, 0},                                  // 传感器6: A31 (ADC)
    {6, false, (uint32_t)GPIOB_BASE, DL_GPIO_PIN_15}   // 传感器7: B15
};

// ================================
// 数据结构定义
// ================================
typedef struct {
    uint16_t values[GRAYSCALE_SENSOR_COUNT];    // 传感器值
    float position;                             // 线条位置
    bool detected;                              // 检测状态
} GrayScale_Data_t;

// ================================
// 全局变量
// ================================
static GrayScale_Data_t g_sensor_data;

// ================================
// 灰度传感器函数实现
// ================================

/**
 * @brief 初始化灰度系统 - 简化版本
 */
int GrayScale_Init(void) {
    // GPIO和ADC配置已在SYSCFG_DL_init()中完成
    // 这里只做基本的验证
    return 0;
}

/**
 * @brief 读取单个传感器 - 适配您的引脚
 */
uint16_t GrayScale_ReadSingle(uint8_t channel) {
    if (channel >= GRAYSCALE_SENSOR_COUNT) {
        return 0;
    }
    
    const sensor_pin_config_t* config = &pin_config[channel];
    uint16_t value = 0;
    
    if (config->is_adc) {
        // ADC引脚读取 (A28, A31)
        // 简化版本：使用现有ADC配置读取
        DL_ADC12_startConversion(ADC_VOLTAGE_INST);
        
        // 等待转换完成
        volatile uint32_t timeout = 10000;
        while (timeout-- > 0) {
            if (DL_ADC12_getStatus(ADC_VOLTAGE_INST) & DL_ADC12_STATUS_CONVERSION_RESULT_LOADED) {
                break;
            }
        }
        
        value = DL_ADC12_getMemResult(ADC_VOLTAGE_INST, ADC_VOLTAGE_ADCMEM_ADC_CH0);
    } else {
        // GPIO引脚读取 (B12, B17, B04, B01, B15)
        // 读取数字输入状态
        bool pin_state = DL_GPIO_readPins((DL_GPIO_Regs*)config->gpio_port, config->gpio_pin);
        value = pin_state ? 4095 : 0;  // 数字信号转换为模拟值
    }
    
    return value;
}

/**
 * @brief 计算线条位置
 */
float GrayScale_CalcPosition(const GrayScale_Data_t* data) {
    if (!data) {
        return 0.0f;
    }
    
    // 简单的位置计算
    const float weights[GRAYSCALE_SENSOR_COUNT] = {
        -3.0f, -2.0f, -1.0f, 0.0f, 1.0f, 2.0f, 3.0f
    };
    
    float sum = 0.0f;
    float weight_sum = 0.0f;
    
    for (int i = 0; i < GRAYSCALE_SENSOR_COUNT; i++) {
        if (data->values[i] > DETECTION_THRESHOLD) {
            float w = (float)data->values[i] / ADC_MAX_VALUE;
            sum += weights[i] * w;
            weight_sum += w;
        }
    }
    
    if (weight_sum > 0.0f) {
        return sum / weight_sum;
    } else {
        return 0.0f;
    }
}

/**
 * @brief 读取所有传感器
 */
int GrayScale_ReadAll(GrayScale_Data_t* data) {
    if (!data) {
        return -1;
    }
    
    // 读取所有传感器
    for (int i = 0; i < GRAYSCALE_SENSOR_COUNT; i++) {
        data->values[i] = GrayScale_ReadSingle(i);
    }
    
    // 计算位置
    data->position = GrayScale_CalcPosition(data);
    
    // 检测线条
    data->detected = false;
    for (int i = 0; i < GRAYSCALE_SENSOR_COUNT; i++) {
        if (data->values[i] > DETECTION_THRESHOLD) {
            data->detected = true;
            break;
        }
    }
    
    return 0;
}

// ================================
// UART通信函数
// ================================

/**
 * @brief UART字符串发送函数
 */
void uart0_send_string(char* str) {
    while(*str != 0 && str != 0) {
        DL_UART_transmitDataBlocking(UART_0_INST, *str++);
    }
}

/**
 * @brief 显示传感器原始数据 - 标注引脚
 */
void display_sensor_data(const GrayScale_Data_t* data) {
    char show_buff[300] = {0};
    
    // 显示7路传感器的原始值，并标注对应引脚
    sprintf(show_buff, "B12[%4d] B17[%4d] B04[%4d] B01[%4d] A28[%4d] A31[%4d] B15[%4d] | ",
        data->values[0],  // B12
        data->values[1],  // B17
        data->values[2],  // B04
        data->values[3],  // B01
        data->values[4],  // A28
        data->values[5],  // A31
        data->values[6]   // B15
    );
    uart0_send_string(show_buff);
}

// ================================
// 主程序
// ================================

int main(void) {
    char show_buff[200] = {0};
    int result;
    
    // 系统初始化
    SYSCFG_DL_init();
    
    uart0_send_string("=== MSPM0G3507 七路灰度循迹系统启动 (引脚适配版) ===\r\n");
    uart0_send_string("引脚配置: B12,B17,B04,B01,A28,A31,B15\r\n");
    
    // 初始化灰度系统
    result = GrayScale_Init();
    if (result != 0) {
        uart0_send_string("灰度系统初始化失败\r\n");
        while(1); // 停止执行
    }
    
    uart0_send_string("灰度系统初始化成功\r\n");
    uart0_send_string("开始七路传感器数据采集...\r\n");
    uart0_send_string("数据格式: B12[值] B17[值] B04[值] B01[值] A28[值] A31[值] B15[值]\r\n");
    
    // 主循环
    while (1) {
        // 读取传感器数据
        result = GrayScale_ReadAll(&g_sensor_data);
        if (result == 0) {
            // 显示传感器数据
            display_sensor_data(&g_sensor_data);
            
            // 计算简单控制输出
            float control_output = g_sensor_data.position * 10.0f; // 简单比例控制
            
            // 显示控制结果
            sprintf(show_buff, "位置:%.2f | 控制:%.1f | 检测:%s\r\n",
                g_sensor_data.position,
                control_output,
                g_sensor_data.detected ? "是" : "否"
            );
            uart0_send_string(show_buff);
        } else {
            uart0_send_string("读取传感器数据失败\r\n");
        }
        
        // 延时，控制输出频率
        delay_ms(100); // 10Hz输出频率
    }
}
