/*
 * MSPM0G3507 七路灰度循迹模块 - 零报错版本
 * 引脚配置: B12,B17,B04,B01,A28,A31,B15
 * 
 * 作者: <PERSON> (工程师)
 * 版本: v1.0-zero-error
 * 日期: 2025-07-10
 */

#include "ti_msp_dl_config.h"

// ================================
// 基本常量定义
// ================================
#define SENSOR_COUNT    7
#define ADC_MAX         4095
#define THRESHOLD       2000

// ================================
// 延时函数 - 使用最基本的实现
// ================================
void simple_delay_ms(uint32_t ms) {
    volatile uint32_t i, j;
    for (i = 0; i < ms; i++) {
        for (j = 0; j < 8000; j++) {
            // 简单延时循环
        }
    }
}

// ================================
// UART发送函数 - 最简化实现
// ================================
void send_char(char c) {
    DL_UART_transmitDataBlocking(UART_0_INST, c);
}

void send_string(const char* str) {
    while (*str) {
        send_char(*str++);
    }
}

void send_number(uint16_t num) {
    char buffer[8];
    int i = 0;
    
    if (num == 0) {
        send_char('0');
        return;
    }
    
    while (num > 0) {
        buffer[i++] = '0' + (num % 10);
        num /= 10;
    }
    
    while (i > 0) {
        send_char(buffer[--i]);
    }
}

// ================================
// 传感器读取函数 - 最简化实现
// ================================
uint16_t read_sensor(uint8_t channel) {
    uint16_t value = 0;
    
    switch (channel) {
        case 0: // B12
            value = DL_GPIO_readPins(GPIOB, DL_GPIO_PIN_12) ? ADC_MAX : 0;
            break;
        case 1: // B17
            value = DL_GPIO_readPins(GPIOB, DL_GPIO_PIN_17) ? ADC_MAX : 0;
            break;
        case 2: // B04
            value = DL_GPIO_readPins(GPIOB, DL_GPIO_PIN_4) ? ADC_MAX : 0;
            break;
        case 3: // B01
            value = DL_GPIO_readPins(GPIOB, DL_GPIO_PIN_1) ? ADC_MAX : 0;
            break;
        case 4: // A28 - ADC
            DL_ADC12_startConversion(ADC_VOLTAGE_INST);
            // 简单等待
            simple_delay_ms(1);
            value = DL_ADC12_getMemResult(ADC_VOLTAGE_INST, ADC_VOLTAGE_ADCMEM_ADC_CH0);
            break;
        case 5: // A31 - ADC
            DL_ADC12_startConversion(ADC_VOLTAGE_INST);
            // 简单等待
            simple_delay_ms(1);
            value = DL_ADC12_getMemResult(ADC_VOLTAGE_INST, ADC_VOLTAGE_ADCMEM_ADC_CH0);
            break;
        case 6: // B15
            value = DL_GPIO_readPins(GPIOB, DL_GPIO_PIN_15) ? ADC_MAX : 0;
            break;
        default:
            value = 0;
            break;
    }
    
    return value;
}

// ================================
// 位置计算函数 - 简化算法
// ================================
float calculate_position(uint16_t sensors[SENSOR_COUNT]) {
    float weights[SENSOR_COUNT] = {-3.0f, -2.0f, -1.0f, 0.0f, 1.0f, 2.0f, 3.0f};
    float sum = 0.0f;
    float weight_sum = 0.0f;
    
    for (int i = 0; i < SENSOR_COUNT; i++) {
        if (sensors[i] > THRESHOLD) {
            float w = (float)sensors[i] / ADC_MAX;
            sum += weights[i] * w;
            weight_sum += w;
        }
    }
    
    return (weight_sum > 0.0f) ? (sum / weight_sum) : 0.0f;
}

// ================================
// 线条检测函数
// ================================
int detect_line(uint16_t sensors[SENSOR_COUNT]) {
    for (int i = 0; i < SENSOR_COUNT; i++) {
        if (sensors[i] > THRESHOLD) {
            return 1; // 检测到线条
        }
    }
    return 0; // 未检测到线条
}

// ================================
// 数据显示函数
// ================================
void display_data(uint16_t sensors[SENSOR_COUNT], float position, int line_detected) {
    // 显示传感器数据
    send_string("B12[");
    send_number(sensors[0]);
    send_string("] B17[");
    send_number(sensors[1]);
    send_string("] B04[");
    send_number(sensors[2]);
    send_string("] B01[");
    send_number(sensors[3]);
    send_string("] A28[");
    send_number(sensors[4]);
    send_string("] A31[");
    send_number(sensors[5]);
    send_string("] B15[");
    send_number(sensors[6]);
    send_string("] | ");
    
    // 显示位置 (简化显示)
    send_string("位置:");
    if (position > 0) {
        send_char('+');
    } else if (position < 0) {
        send_char('-');
        position = -position;
    }
    
    // 简化的浮点数显示
    uint16_t int_part = (uint16_t)position;
    uint16_t frac_part = (uint16_t)((position - int_part) * 100);
    send_number(int_part);
    send_char('.');
    if (frac_part < 10) send_char('0');
    send_number(frac_part);
    
    // 显示检测状态
    send_string(" | 检测:");
    send_string(line_detected ? "是" : "否");
    send_string("\r\n");
}

// ================================
// 主程序
// ================================
int main(void) {
    uint16_t sensor_values[SENSOR_COUNT];
    float position;
    int line_detected;
    
    // 系统初始化
    SYSCFG_DL_init();
    
    // 启动信息
    send_string("=== MSPM0G3507 七路灰度循迹系统 (零报错版) ===\r\n");
    send_string("引脚: B12,B17,B04,B01,A28,A31,B15\r\n");
    send_string("系统初始化完成\r\n");
    send_string("开始数据采集...\r\n\r\n");
    
    // 主循环
    while (1) {
        // 读取所有传感器
        for (int i = 0; i < SENSOR_COUNT; i++) {
            sensor_values[i] = read_sensor(i);
        }
        
        // 计算位置
        position = calculate_position(sensor_values);
        
        // 检测线条
        line_detected = detect_line(sensor_values);
        
        // 显示数据
        display_data(sensor_values, position, line_detected);
        
        // 延时
        simple_delay_ms(100);
    }
    
    return 0;
}
