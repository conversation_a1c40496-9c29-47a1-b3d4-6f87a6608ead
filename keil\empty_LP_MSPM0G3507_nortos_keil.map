Component: Arm Compiler for Embedded 6.21 Tool: armlink [5ec1fa00]

==============================================================================

Section Cross References

    empty.o(.text) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    empty.o(.text) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    empty.o(.text) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    empty.o(.text) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    empty.o(.text) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    empty.o(.text.main) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    empty.o(.text.main) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    empty.o(.text.main) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    empty.o(.text.main) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    empty.o(.text.main) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    empty.o(.text.main) refers to rt_memclr.o(.text) for __aeabi_memclr4
    empty.o(.text.main) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_init) for SYSCFG_DL_init
    empty.o(.text.main) refers to dl_uart.o(.text.DL_UART_transmitDataBlocking) for DL_UART_transmitDataBlocking
    empty.o(.text.main) refers to dl_common.o(.text.DL_Common_delayCycles) for DL_Common_delayCycles
    empty.o(.text.main) refers to noretval__2sprintf.o(.text) for __2sprintf
    empty.o(.text.main) refers to dflti.o(.text) for __aeabi_ui2d
    empty.o(.text.main) refers to ddiv.o(.text) for __aeabi_ddiv
    empty.o(.text.main) refers to dmul.o(.text) for __aeabi_dmul
    empty.o(.text.main) refers to d2f.o(.text) for __aeabi_d2f
    empty.o(.text.main) refers to f2d.o(.text) for __aeabi_f2d
    empty.o(.text.main) refers to empty.o(.bss.gCheckADC) for gCheckADC
    empty.o(.ARM.exidx.text.main) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    empty.o(.ARM.exidx.text.main) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    empty.o(.ARM.exidx.text.main) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    empty.o(.ARM.exidx.text.main) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    empty.o(.ARM.exidx.text.main) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    empty.o(.ARM.exidx.text.main) refers to empty.o(.text.main) for [Anonymous Symbol]
    empty.o(.text.uart0_send_string) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    empty.o(.text.uart0_send_string) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    empty.o(.text.uart0_send_string) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    empty.o(.text.uart0_send_string) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    empty.o(.text.uart0_send_string) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    empty.o(.text.uart0_send_string) refers to dl_uart.o(.text.DL_UART_transmitDataBlocking) for DL_UART_transmitDataBlocking
    empty.o(.ARM.exidx.text.uart0_send_string) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    empty.o(.ARM.exidx.text.uart0_send_string) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    empty.o(.ARM.exidx.text.uart0_send_string) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    empty.o(.ARM.exidx.text.uart0_send_string) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    empty.o(.ARM.exidx.text.uart0_send_string) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    empty.o(.ARM.exidx.text.uart0_send_string) refers to empty.o(.text.uart0_send_string) for [Anonymous Symbol]
    empty.o(.text.adc_getValue) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    empty.o(.text.adc_getValue) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    empty.o(.text.adc_getValue) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    empty.o(.text.adc_getValue) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    empty.o(.text.adc_getValue) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    empty.o(.text.adc_getValue) refers to empty.o(.bss.gCheckADC) for gCheckADC
    empty.o(.ARM.exidx.text.adc_getValue) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    empty.o(.ARM.exidx.text.adc_getValue) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    empty.o(.ARM.exidx.text.adc_getValue) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    empty.o(.ARM.exidx.text.adc_getValue) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    empty.o(.ARM.exidx.text.adc_getValue) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    empty.o(.ARM.exidx.text.adc_getValue) refers to empty.o(.text.adc_getValue) for [Anonymous Symbol]
    empty.o(.text.ADC0_IRQHandler) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    empty.o(.text.ADC0_IRQHandler) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    empty.o(.text.ADC0_IRQHandler) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    empty.o(.text.ADC0_IRQHandler) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    empty.o(.text.ADC0_IRQHandler) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    empty.o(.text.ADC0_IRQHandler) refers to empty.o(.bss.gCheckADC) for gCheckADC
    empty.o(.ARM.exidx.text.ADC0_IRQHandler) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    empty.o(.ARM.exidx.text.ADC0_IRQHandler) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    empty.o(.ARM.exidx.text.ADC0_IRQHandler) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    empty.o(.ARM.exidx.text.ADC0_IRQHandler) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    empty.o(.ARM.exidx.text.ADC0_IRQHandler) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    empty.o(.ARM.exidx.text.ADC0_IRQHandler) refers to empty.o(.text.ADC0_IRQHandler) for [Anonymous Symbol]
    empty.o(.bss.gCheckADC) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    empty.o(.bss.gCheckADC) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    empty.o(.bss.gCheckADC) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    empty.o(.bss.gCheckADC) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    empty.o(.bss.gCheckADC) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    empty.o(.ARM.use_no_argv) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    empty.o(.ARM.use_no_argv) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    empty.o(.ARM.use_no_argv) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    empty.o(.ARM.use_no_argv) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    empty.o(.ARM.use_no_argv) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    startup_mspm0g350x_uvision.o(STACK) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_mspm0g350x_uvision.o(HEAP) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_mspm0g350x_uvision.o(RESET) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_mspm0g350x_uvision.o(RESET) refers to startup_mspm0g350x_uvision.o(STACK) for __initial_sp
    startup_mspm0g350x_uvision.o(RESET) refers to startup_mspm0g350x_uvision.o(.text) for Reset_Handler
    startup_mspm0g350x_uvision.o(RESET) refers to empty.o(.text.ADC0_IRQHandler) for ADC0_IRQHandler
    startup_mspm0g350x_uvision.o(.text) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_mspm0g350x_uvision.o(.text) refers to __main.o(!!!main) for __main
    startup_mspm0g350x_uvision.o(.text) refers to startup_mspm0g350x_uvision.o(HEAP) for Heap_Mem
    startup_mspm0g350x_uvision.o(.text) refers to startup_mspm0g350x_uvision.o(STACK) for Stack_Mem
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_initPower) for SYSCFG_DL_initPower
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) for SYSCFG_DL_GPIO_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) for SYSCFG_DL_SYSCTL_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) for SYSCFG_DL_UART_0_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_ADC_VOLTAGE_init) for SYSCFG_DL_ADC_VOLTAGE_init
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_initPower) refers to dl_common.o(.text.DL_Common_delayCycles) for DL_Common_delayCycles
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_initPower) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_initPower) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_GPIO_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_SYSCTL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) refers to dl_uart.o(.text.DL_UART_setClockConfig) for DL_UART_setClockConfig
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) refers to dl_uart.o(.text.DL_UART_init) for DL_UART_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) refers to ti_msp_dl_config.o(.rodata.gUART_0ClockConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) refers to ti_msp_dl_config.o(.rodata.gUART_0Config) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_UART_0_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_ADC_VOLTAGE_init) refers to dl_adc12.o(.text.DL_ADC12_setClockConfig) for DL_ADC12_setClockConfig
    ti_msp_dl_config.o(.text.SYSCFG_DL_ADC_VOLTAGE_init) refers to ti_msp_dl_config.o(.rodata.gADC_VOLTAGEClockConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_ADC_VOLTAGE_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_ADC_VOLTAGE_init) for [Anonymous Symbol]
    dl_adc12.o(.ARM.exidx.text.DL_ADC12_setClockConfig) refers to dl_adc12.o(.text.DL_ADC12_setClockConfig) for [Anonymous Symbol]
    dl_adc12.o(.ARM.exidx.text.DL_ADC12_getClockConfig) refers to dl_adc12.o(.text.DL_ADC12_getClockConfig) for [Anonymous Symbol]
    dl_common.o(.ARM.exidx.text.DL_Common_delayCycles) refers to dl_common.o(.text.DL_Common_delayCycles) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_init) refers to dl_uart.o(.text.DL_UART_init) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_setClockConfig) refers to dl_uart.o(.text.DL_UART_setClockConfig) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_getClockConfig) refers to dl_uart.o(.text.DL_UART_getClockConfig) for [Anonymous Symbol]
    dl_uart.o(.text.DL_UART_configBaudRate) refers to aeabi_sdivfast.o(.text_divfast) for __aeabi_uidiv
    dl_uart.o(.ARM.exidx.text.DL_UART_configBaudRate) refers to dl_uart.o(.text.DL_UART_configBaudRate) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_configIrDAMode) refers to dl_uart.o(.text.DL_UART_configIrDAMode) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_setIrDAPulseLength) refers to dl_uart.o(.text.DL_UART_setIrDAPulseLength) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_receiveDataBlocking) refers to dl_uart.o(.text.DL_UART_receiveDataBlocking) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_transmitDataBlocking) refers to dl_uart.o(.text.DL_UART_transmitDataBlocking) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_receiveDataCheck) refers to dl_uart.o(.text.DL_UART_receiveDataCheck) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_transmitDataCheck) refers to dl_uart.o(.text.DL_UART_transmitDataCheck) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_drainRXFIFO) refers to dl_uart.o(.text.DL_UART_drainRXFIFO) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_fillTXFIFO) refers to dl_uart.o(.text.DL_UART_fillTXFIFO) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_Main_saveConfiguration) refers to dl_uart.o(.text.DL_UART_Main_saveConfiguration) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_Main_restoreConfiguration) refers to dl_uart.o(.text.DL_UART_Main_restoreConfiguration) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_Extend_saveConfiguration) refers to dl_uart.o(.text.DL_UART_Extend_saveConfiguration) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_Extend_restoreConfiguration) refers to dl_uart.o(.text.DL_UART_Extend_restoreConfiguration) for [Anonymous Symbol]
    __2sprintf.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    __2sprintf.o(.text) refers to _sputc.o(.text) for _sputc
    noretval__2sprintf.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    noretval__2sprintf.o(.text) refers to _sputc.o(.text) for _sputc
    __printf.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    _printf_dec.o(.text) refers to rtudiv10.o(.text) for __rt_udiv10
    _printf_dec.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    __printf_flags.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags.o(.text) refers to __printf_flags.o(.constdata) for .constdata
    __printf_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to __printf_flags_ss.o(.constdata) for .constdata
    __printf_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_flags_wp.o(.constdata) for .constdata
    __printf_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_flags_ss_wp.o(.constdata) for .constdata
    _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) refers (Weak) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) refers (Special) to _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017) for _printf_percent_end
    __main.o(!!!main) refers to __rtentry.o(.ARM.Collect$$rtentry$$00000000) for __rt_entry
    d2f.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ddiv.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ddiv.o(.text) refers to ddiv.o(.constdata) for .constdata
    ddiv.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dflti.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dmul.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    f2d.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    printf1.o(x$fpl$printf1) refers to _printf_fp_dec.o(.text) for _printf_fp_dec_real
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for __rt_entry_li
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for __rt_entry_main
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000C) for __rt_entry_postli_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000009) for __rt_entry_postsh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000002) for __rt_entry_presh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for __rt_entry_sh
    aeabi_idiv0_sigfpe.o(.text) refers to rt_div0.o(.text) for __rt_div0
    _printf_fp_dec.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    _printf_fp_dec.o(.text) refers (Special) to lc_numeric_c.o(locale$$code) for _get_lc_numeric
    _printf_fp_dec.o(.text) refers to bigflt0.o(.text) for _btod_etento
    _printf_fp_dec.o(.text) refers to btod.o(.text) for _btod_d2e
    _printf_fp_dec.o(.text) refers to lludiv10.o(.text) for _ll_udiv10
    _printf_fp_dec.o(.text) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    _printf_fp_dec.o(.text) refers to _printf_fp_infnan.o(.text) for _printf_fp_infnan
    _printf_fp_dec.o(.text) refers to rtudiv10.o(.text) for __rt_udiv10
    _printf_fp_dec.o(.text) refers to rt_locale_intlibspace.o(.text) for __rt_locale
    _printf_fp_dec_accurate.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    _printf_fp_dec_accurate.o(.text) refers (Special) to lc_numeric_c.o(locale$$code) for _get_lc_numeric
    _printf_fp_dec_accurate.o(.text) refers to btod_accurate.o(.text) for _btod_main
    _printf_fp_dec_accurate.o(.text) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    _printf_fp_dec_accurate.o(.text) refers to _printf_fp_infnan.o(.text) for _printf_fp_infnan
    _printf_fp_dec_accurate.o(.text) refers to rtudiv10.o(.text) for __rt_udiv10
    _printf_fp_dec_accurate.o(.text) refers to rt_locale_intlibspace.o(.text) for __rt_locale
    _printf_char_common.o(.text) refers to __printf_wp.o(.text) for __printf
    __rtentry2.o(.ARM.Collect$$rtentry$$00000008) refers to boardinit2.o(.text) for _platform_post_stackheap_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) refers to libinit.o(.ARM.Collect$$libinit$$00000000) for __rt_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) refers to boardinit3.o(.text) for _platform_post_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to empty.o(.text.main) for main
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to exit.o(.text) for exit
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000001) for .ARM.Collect$$rtentry$$00000001
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000008) for .ARM.Collect$$rtentry$$00000008
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for .ARM.Collect$$rtentry$$0000000A
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) for .ARM.Collect$$rtentry$$0000000B
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for .ARM.Collect$$rtentry$$0000000D
    __rtentry4.o(.ARM.Collect$$rtentry$$00000004) refers to sys_stackheap_outer.o(.text) for __user_setup_stackheap
    __rtentry4.o(.ARM.exidx) refers to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for .ARM.Collect$$rtentry$$00000004
    rt_div0.o(.text) refers to defsig_fpe_outer.o(.text) for __rt_SIGFPE
    rt_locale.o(.text) refers to rt_locale.o(.bss) for __rt_locale_data
    rt_locale_intlibspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    bigflt0.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    bigflt0.o(.text) refers to aeabi_sdivfast.o(.text) for __aeabi_idivmod
    bigflt0.o(.text) refers to btod.o(.text) for _btod_emul
    bigflt0.o(.text) refers to bigflt0.o(.constdata) for .constdata
    bigflt0.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(.text) refers to btod.o(i.__ARM_common_ll_muluu) for __ARM_common_ll_muluu
    btod_accurate.o(.text) refers to btod_accurate_common.o(.text) for _btod_common
    lc_numeric_c.o(locale$$data) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000018) for __rt_lib_init_lc_numeric_2
    lc_numeric_c.o(locale$$code) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000018) for __rt_lib_init_lc_numeric_2
    lc_numeric_c.o(locale$$code) refers to strcmpv6m.o(.text) for strcmp
    lc_numeric_c.o(locale$$code) refers to lc_numeric_c.o(locale$$data) for __lcnum_c_name
    ieee_status.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fpclassify.o(i.__ARM_fpclassify) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    libspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    sys_stackheap_outer.o(.text) refers to libspace.o(.text) for __user_perproc_libspace
    sys_stackheap_outer.o(.text) refers to startup_mspm0g350x_uvision.o(.text) for __user_initial_stackheap
    sys_stackheap_outer.o(__vectab_stack_and_reset_area) refers to tempstk.o(.text) for __temporary_stack_top
    sys_stackheap_outer.o(__vectab_stack_and_reset_area) refers to __main.o(!!!main) for __main
    btod_accurate_common.o(.text) refers to aeabi_sdivfast.o(.text) for __aeabi_uidivmod
    btod_accurate_common.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    exit.o(.text) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for __rt_exit
    defsig_fpe_outer.o(.text) refers to defsig_fpe_inner.o(.text) for __rt_SIGFPE_inner
    defsig_fpe_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_fpe_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000030) for __rt_lib_init_alloca_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002E) for __rt_lib_init_argv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001D) for __rt_lib_init_atexit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000023) for __rt_lib_init_clock_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000034) for __rt_lib_init_cpp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000032) for __rt_lib_init_exceptions_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000002) for __rt_lib_init_fp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000021) for __rt_lib_init_fp_trap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000025) for __rt_lib_init_getenv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000C) for __rt_lib_init_heap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000013) for __rt_lib_init_lc_collate_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000015) for __rt_lib_init_lc_ctype_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000017) for __rt_lib_init_lc_monetary_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000019) for __rt_lib_init_lc_numeric_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001B) for __rt_lib_init_lc_time_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000006) for __rt_lib_init_preinit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000010) for __rt_lib_init_rand_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000004) for __rt_lib_init_relocate_pie_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000035) for __rt_lib_init_return
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001F) for __rt_lib_init_signal_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000027) for __rt_lib_init_stdio_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000E) for __rt_lib_init_user_alloc_1
    libinit2.o(.ARM.Collect$$libinit$$00000011) refers (Weak) to rt_locale_intlibspace.o(.text) for __rt_locale
    libinit2.o(.ARM.Collect$$libinit$$00000012) refers to libinit2.o(.ARM.Collect$$libinit$$00000011) for .ARM.Collect$$libinit$$00000011
    libinit2.o(.ARM.Collect$$libinit$$00000014) refers to libinit2.o(.ARM.Collect$$libinit$$00000011) for .ARM.Collect$$libinit$$00000011
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers to libinit2.o(.ARM.Collect$$libinit$$00000011) for .ARM.Collect$$libinit$$00000011
    libinit2.o(.ARM.Collect$$libinit$$00000018) refers to libinit2.o(.ARM.Collect$$libinit$$00000011) for .ARM.Collect$$libinit$$00000011
    libinit2.o(.ARM.Collect$$libinit$$00000018) refers (Weak) to lc_numeric_c.o(locale$$code) for _get_lc_numeric
    libinit2.o(.ARM.Collect$$libinit$$0000001A) refers to libinit2.o(.ARM.Collect$$libinit$$00000011) for .ARM.Collect$$libinit$$00000011
    libinit2.o(.ARM.Collect$$libinit$$00000028) refers to argv_veneer.o(.text) for __ARM_argv_veneer
    libinit2.o(.ARM.Collect$$libinit$$00000029) refers to argv_veneer.o(.text) for __ARM_argv_veneer
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for .ARM.Collect$$rtexit$$00000000
    rt_raise.o(.text) refers to __raise.o(.text) for __raise
    rt_raise.o(.text) refers to sys_exit.o(.text) for _sys_exit
    defsig_exit.o(.text) refers to sys_exit.o(.text) for _sys_exit
    defsig_fpe_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    sys_exit.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_exit.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    sys_exit_hlt.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_exit_hlt.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    rtexit2.o(.ARM.Collect$$rtexit$$00000003) refers to libshutdown.o(.ARM.Collect$$libshutdown$$00000000) for __rt_lib_shutdown
    rtexit2.o(.ARM.Collect$$rtexit$$00000004) refers to sys_exit.o(.text) for _sys_exit
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000001) for .ARM.Collect$$rtexit$$00000001
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for .ARM.Collect$$rtexit$$00000003
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for .ARM.Collect$$rtexit$$00000004
    __raise.o(.text) refers to defsig.o(CL$$defsig) for __default_signal_handler
    defsig_general.o(.text) refers to sys_wrch.o(.text) for _ttywrch
    _get_argv_nomalloc.o(.text) refers (Special) to hrguard.o(.text) for __heap_region$guard
    _get_argv_nomalloc.o(.text) refers to defsig_rtmem_outer.o(.text) for __rt_SIGRTMEM
    _get_argv_nomalloc.o(.text) refers to sys_command.o(.text) for _sys_command_string
    sys_wrch.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_wrch.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    sys_command.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_command.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    sys_wrch_hlt.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_wrch_hlt.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    sys_command_hlt.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_command_hlt.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig_rtmem_outer.o(.text) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_rtmem_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_rtmem_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    defsig.o(CL$$defsig) refers to defsig_fpe_inner.o(.text) for __rt_SIGFPE_inner
    defsig.o(CL$$defsig) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000002) for __rt_lib_shutdown_cpp_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000007) for __rt_lib_shutdown_fp_trap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F) for __rt_lib_shutdown_heap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000010) for __rt_lib_shutdown_return
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A) for __rt_lib_shutdown_signal_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000004) for __rt_lib_shutdown_stdio_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C) for __rt_lib_shutdown_user_alloc_1
    defsig_abrt_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtred_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtmem_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_stak_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_pvfn_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_cppl_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_segv_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_other.o(.text) refers to defsig_general.o(.text) for __default_signal_display


==============================================================================

Removing Unused input sections from the image.

    Removing empty.o(.text), (0 bytes).
    Removing empty.o(.ARM.exidx.text.main), (8 bytes).
    Removing empty.o(.text.uart0_send_string), (36 bytes).
    Removing empty.o(.ARM.exidx.text.uart0_send_string), (8 bytes).
    Removing empty.o(.text.adc_getValue), (72 bytes).
    Removing empty.o(.ARM.exidx.text.adc_getValue), (8 bytes).
    Removing empty.o(.ARM.exidx.text.ADC0_IRQHandler), (8 bytes).
    Removing empty.o(.ARM.use_no_argv), (4 bytes).
    Removing ti_msp_dl_config.o(.text), (0 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_initPower), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_GPIO_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_SYSCTL_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_UART_0_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_ADC_VOLTAGE_init), (8 bytes).
    Removing dl_adc12.o(.text), (0 bytes).
    Removing dl_adc12.o(.ARM.exidx.text.DL_ADC12_setClockConfig), (8 bytes).
    Removing dl_adc12.o(.text.DL_ADC12_getClockConfig), (40 bytes).
    Removing dl_adc12.o(.ARM.exidx.text.DL_ADC12_getClockConfig), (8 bytes).
    Removing dl_common.o(.text), (0 bytes).
    Removing dl_common.o(.ARM.exidx.text.DL_Common_delayCycles), (8 bytes).
    Removing dl_uart.o(.text), (0 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_init), (8 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_setClockConfig), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_getClockConfig), (16 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_getClockConfig), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_configBaudRate), (140 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_configBaudRate), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_configIrDAMode), (60 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_configIrDAMode), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_setIrDAPulseLength), (42 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_setIrDAPulseLength), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_receiveDataBlocking), (20 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_receiveDataBlocking), (8 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_transmitDataBlocking), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_receiveDataCheck), (28 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_receiveDataCheck), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_transmitDataCheck), (24 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_transmitDataCheck), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_drainRXFIFO), (40 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_drainRXFIFO), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_fillTXFIFO), (40 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_fillTXFIFO), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_Main_saveConfiguration), (88 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_Main_saveConfiguration), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_Main_restoreConfiguration), (120 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_Main_restoreConfiguration), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_Extend_saveConfiguration), (104 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_Extend_saveConfiguration), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_Extend_restoreConfiguration), (132 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_Extend_restoreConfiguration), (8 bytes).

51 unused section(s) (total 1238 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit1.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit2.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit3.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardshut.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_zi.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry4.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit2.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  aeabi_idiv0.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  aeabi_idiv0_sigfpe.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_div0.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_locale.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_locale_intlibspace.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_raise.o ABSOLUTE
    ../clib/angel/scatterp.s                 0x00000000   Number         0  __scatter.o ABSOLUTE
    ../clib/angel/startup.s                  0x00000000   Number         0  __main.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  libspace.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  sys_stackheap_outer.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  tempstk.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  use_no_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  indicate_semi.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_exit.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_exit_hlt.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_wrch.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_command.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_wrch_hlt.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_command_hlt.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  _get_argv_nomalloc.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  no_argv.o ABSOLUTE
    ../clib/bigflt.c                         0x00000000   Number         0  bigflt0.o ABSOLUTE
    ../clib/btod.c                           0x00000000   Number         0  btod.o ABSOLUTE
    ../clib/btod_accurate.c                  0x00000000   Number         0  btod_accurate.o ABSOLUTE
    ../clib/btod_accurate.c                  0x00000000   Number         0  btod_accurate_common.o ABSOLUTE
    ../clib/division.c                       0x00000000   Number         0  rtudiv10.o ABSOLUTE
    ../clib/division.c                       0x00000000   Number         0  lludiv10.o ABSOLUTE
    ../clib/division.s                       0x00000000   Number         0  aeabi_sdivfast.o ABSOLUTE
    ../clib/division.s                       0x00000000   Number         0  aeabi_sdivfast_div0.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  hrguard.o ABSOLUTE
    ../clib/heapaux.c                        0x00000000   Number         0  heapauxi.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown2.o ABSOLUTE
    ../clib/locale.s                         0x00000000   Number         0  lc_numeric_c.o ABSOLUTE
    ../clib/longlong.c                       0x00000000   Number         0  llushr.o ABSOLUTE
    ../clib/memcpset.c                       0x00000000   Number         0  rt_memclr.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  strcmpv6m.o ABSOLUTE
    ../clib/misc.s                           0x00000000   Number         0  printf_stubs.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __2sprintf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  noretval__2sprintf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_ss.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_ss.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_ss_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_ss_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_intcommon.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_fp_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_fp_dec_accurate.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char_common.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _sputc.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_fp_infnan.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_nopercent.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_d.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_f.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_percent.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_percent_end.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_exit.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  __raise.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_general.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_abrt_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtred_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_stak_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_pvfn_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_cppl_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_segv_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_other.o ABSOLUTE
    ../clib/signal.s                         0x00000000   Number         0  defsig.o ABSOLUTE
    ../clib/stdlib.c                         0x00000000   Number         0  exit.o ABSOLUTE
    ../fplib/cfplib/d2f.c                    0x00000000   Number         0  d2f.o ABSOLUTE
    ../fplib/cfplib/ddiv.c                   0x00000000   Number         0  ddiv.o ABSOLUTE
    ../fplib/cfplib/dmul.c                   0x00000000   Number         0  dmul.o ABSOLUTE
    ../fplib/cfplib/f2d.c                    0x00000000   Number         0  f2d.o ABSOLUTE
    ../fplib/cfplib/fflt.c                   0x00000000   Number         0  dflti.o ABSOLUTE
    ../fplib/cfplib/fpinit.c                 0x00000000   Number         0  fpinit.o ABSOLUTE
    ../fplib/cfplib/ieee_status.c            0x00000000   Number         0  ieee_status.o ABSOLUTE
    ../fplib/printf1.s                       0x00000000   Number         0  printf1.o ABSOLUTE
    ../fplib/usenofp.s                       0x00000000   Number         0  usenofp.o ABSOLUTE
    ../mathlib/fpclassify.c                  0x00000000   Number         0  fpclassify.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    dl_adc12.c                               0x00000000   Number         0  dl_adc12.o ABSOLUTE
    dl_common.c                              0x00000000   Number         0  dl_common.o ABSOLUTE
    dl_uart.c                                0x00000000   Number         0  dl_uart.o ABSOLUTE
    empty.c                                  0x00000000   Number         0  empty.o ABSOLUTE
    startup_mspm0g350x_uvision.s             0x00000000   Number         0  startup_mspm0g350x_uvision.o ABSOLUTE
    ti_msp_dl_config.c                       0x00000000   Number         0  ti_msp_dl_config.o ABSOLUTE
    RESET                                    0x00000000   Section      192  startup_mspm0g350x_uvision.o(RESET)
    !!!main                                  0x000000c0   Section        8  __main.o(!!!main)
    !!!scatter                               0x000000c8   Section       84  __scatter.o(!!!scatter)
    !!handler_null                           0x00000120   Section        2  __scatter.o(!!handler_null)
    !!handler_zi                             0x00000128   Section       28  __scatter_zi.o(!!handler_zi)
    .ARM.Collect$$_printf_percent$$00000000  0x00000144   Section        2  _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000)
    .ARM.Collect$$_printf_percent$$00000003  0x00000146   Section       10  _printf_f.o(.ARM.Collect$$_printf_percent$$00000003)
    .ARM.Collect$$_printf_percent$$00000009  0x00000150   Section       10  _printf_d.o(.ARM.Collect$$_printf_percent$$00000009)
    .ARM.Collect$$_printf_percent$$00000017  0x0000015a   Section        4  _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017)
    .ARM.Collect$$libinit$$00000000          0x0000015e   Section        2  libinit.o(.ARM.Collect$$libinit$$00000000)
    .ARM.Collect$$libinit$$00000002          0x00000160   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000002)
    .ARM.Collect$$libinit$$00000004          0x00000160   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    .ARM.Collect$$libinit$$00000006          0x00000160   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000006)
    .ARM.Collect$$libinit$$0000000C          0x00000160   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    .ARM.Collect$$libinit$$0000000E          0x00000160   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    .ARM.Collect$$libinit$$00000010          0x00000160   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000010)
    .ARM.Collect$$libinit$$00000011          0x00000160   Section        6  libinit2.o(.ARM.Collect$$libinit$$00000011)
    .ARM.Collect$$libinit$$00000013          0x00000166   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    .ARM.Collect$$libinit$$00000015          0x00000166   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    .ARM.Collect$$libinit$$00000017          0x00000166   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    .ARM.Collect$$libinit$$00000018          0x00000166   Section       10  libinit2.o(.ARM.Collect$$libinit$$00000018)
    .ARM.Collect$$libinit$$00000019          0x00000170   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    .ARM.Collect$$libinit$$0000001B          0x00000170   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    .ARM.Collect$$libinit$$0000001D          0x00000170   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    .ARM.Collect$$libinit$$0000001F          0x00000170   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    .ARM.Collect$$libinit$$00000021          0x00000170   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    .ARM.Collect$$libinit$$00000023          0x00000170   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    .ARM.Collect$$libinit$$00000025          0x00000170   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    .ARM.Collect$$libinit$$00000027          0x00000170   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000027)
    .ARM.Collect$$libinit$$0000002E          0x00000170   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    .ARM.Collect$$libinit$$00000030          0x00000170   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    .ARM.Collect$$libinit$$00000032          0x00000170   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    .ARM.Collect$$libinit$$00000034          0x00000170   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000034)
    .ARM.Collect$$libinit$$00000035          0x00000170   Section        2  libinit2.o(.ARM.Collect$$libinit$$00000035)
    .ARM.Collect$$libshutdown$$00000000      0x00000172   Section        2  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    .ARM.Collect$$libshutdown$$00000002      0x00000174   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    .ARM.Collect$$libshutdown$$00000004      0x00000174   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    .ARM.Collect$$libshutdown$$00000007      0x00000174   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000007)
    .ARM.Collect$$libshutdown$$0000000A      0x00000174   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A)
    .ARM.Collect$$libshutdown$$0000000C      0x00000174   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    .ARM.Collect$$libshutdown$$0000000F      0x00000174   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F)
    .ARM.Collect$$libshutdown$$00000010      0x00000174   Section        2  libshutdown2.o(.ARM.Collect$$libshutdown$$00000010)
    .ARM.Collect$$rtentry$$00000000          0x00000176   Section        0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    .ARM.Collect$$rtentry$$00000002          0x00000176   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    .ARM.Collect$$rtentry$$00000004          0x00000176   Section        6  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    .ARM.Collect$$rtentry$$00000009          0x0000017c   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    .ARM.Collect$$rtentry$$0000000A          0x0000017c   Section        4  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    .ARM.Collect$$rtentry$$0000000C          0x00000180   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    .ARM.Collect$$rtentry$$0000000D          0x00000180   Section        8  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    .ARM.Collect$$rtexit$$00000000           0x00000188   Section        2  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    .ARM.Collect$$rtexit$$00000002           0x0000018a   Section        0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    .ARM.Collect$$rtexit$$00000003           0x0000018a   Section        4  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    .ARM.Collect$$rtexit$$00000004           0x0000018e   Section        6  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    .text                                    0x00000194   Section       48  startup_mspm0g350x_uvision.o(.text)
    .text                                    0x000001c4   Section        0  noretval__2sprintf.o(.text)
    .text                                    0x000001ec   Section        0  _printf_dec.o(.text)
    .text                                    0x00000258   Section        0  __printf_wp.o(.text)
    .text                                    0x00000366   Section        0  rt_memclr.o(.text)
    .text                                    0x000003a8   Section      504  aeabi_sdivfast.o(.text)
    .text                                    0x000005a0   Section        0  heapauxi.o(.text)
    .text                                    0x000005a8   Section        0  d2f.o(.text)
    .text                                    0x00000624   Section        0  ddiv.o(.text)
    .text                                    0x00000a6c   Section        0  dflti.o(.text)
    .text                                    0x00000ac4   Section        0  dmul.o(.text)
    .text                                    0x00000d0c   Section        0  f2d.o(.text)
    .text                                    0x00000d60   Section        0  _printf_intcommon.o(.text)
    _fp_digits                               0x00000e11   Thumb Code   412  _printf_fp_dec.o(.text)
    .text                                    0x00000e10   Section        0  _printf_fp_dec.o(.text)
    _printf_input_char                       0x00001225   Thumb Code    10  _printf_char_common.o(.text)
    .text                                    0x00001224   Section        0  _printf_char_common.o(.text)
    .text                                    0x00001254   Section        0  _sputc.o(.text)
    .text                                    0x0000125e   Section        0  rtudiv10.o(.text)
    .text                                    0x00001288   Section        8  rt_locale_intlibspace.o(.text)
    .text                                    0x00001290   Section        0  lludiv10.o(.text)
    .text                                    0x0000130c   Section        0  _printf_fp_infnan.o(.text)
    .text                                    0x00001394   Section        0  bigflt0.o(.text)
    .text                                    0x0000146c   Section        0  btod.o(.text)
    btod_internal_mul                        0x000014ad   Thumb Code   492  btod.o(.text)
    btod_internal_div                        0x00001699   Thumb Code   520  btod.o(.text)
    .text                                    0x000019ec   Section        8  libspace.o(.text)
    .text                                    0x000019f4   Section       62  sys_stackheap_outer.o(.text)
    .text                                    0x00001a32   Section        0  exit.o(.text)
    .text                                    0x00001a44   Section      176  strcmpv6m.o(.text)
    .text                                    0x00001af4   Section        0  sys_exit.o(.text)
    .text                                    0x00001b00   Section        2  use_no_semi.o(.text)
    .text                                    0x00001b02   Section        0  indicate_semi.o(.text)
    [Anonymous Symbol]                       0x00001b04   Section        0  empty.o(.text.ADC0_IRQHandler)
    __arm_cp.3_0                             0x00001b18   Number         4  empty.o(.text.ADC0_IRQHandler)
    [Anonymous Symbol]                       0x00001b1c   Section        0  dl_adc12.o(.text.DL_ADC12_setClockConfig)
    __arm_cp.0_0                             0x00001b54   Number         4  dl_adc12.o(.text.DL_ADC12_setClockConfig)
    __arm_cp.0_1                             0x00001b58   Number         4  dl_adc12.o(.text.DL_ADC12_setClockConfig)
    [Anonymous Symbol]                       0x00001b5c   Section        0  dl_common.o(.text.DL_Common_delayCycles)
    [Anonymous Symbol]                       0x00001b68   Section        0  dl_uart.o(.text.DL_UART_init)
    __arm_cp.0_0                             0x00001ba8   Number         4  dl_uart.o(.text.DL_UART_init)
    __arm_cp.0_1                             0x00001bac   Number         4  dl_uart.o(.text.DL_UART_init)
    [Anonymous Symbol]                       0x00001bb0   Section        0  dl_uart.o(.text.DL_UART_setClockConfig)
    [Anonymous Symbol]                       0x00001bc4   Section        0  dl_uart.o(.text.DL_UART_transmitDataBlocking)
    __arm_cp.7_0                             0x00001bd4   Number         4  dl_uart.o(.text.DL_UART_transmitDataBlocking)
    [Anonymous Symbol]                       0x00001bd8   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_ADC_VOLTAGE_init)
    __arm_cp.5_0                             0x00001c24   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_ADC_VOLTAGE_init)
    __arm_cp.5_1                             0x00001c28   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_ADC_VOLTAGE_init)
    __arm_cp.5_2                             0x00001c2c   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_ADC_VOLTAGE_init)
    __arm_cp.5_3                             0x00001c30   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_ADC_VOLTAGE_init)
    __arm_cp.5_4                             0x00001c34   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_ADC_VOLTAGE_init)
    __arm_cp.5_5                             0x00001c38   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_ADC_VOLTAGE_init)
    __arm_cp.5_6                             0x00001c3c   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_ADC_VOLTAGE_init)
    [Anonymous Symbol]                       0x00001c40   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    __arm_cp.2_0                             0x00001c4c   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    __arm_cp.2_1                             0x00001c50   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    [Anonymous Symbol]                       0x00001c54   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init)
    __arm_cp.3_0                             0x00001c8c   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init)
    __arm_cp.3_1                             0x00001c90   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init)
    [Anonymous Symbol]                       0x00001c94   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init)
    __arm_cp.4_1                             0x00001ce0   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init)
    __arm_cp.4_2                             0x00001ce4   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init)
    __arm_cp.4_3                             0x00001ce8   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init)
    __arm_cp.4_4                             0x00001cec   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init)
    [Anonymous Symbol]                       0x00001cf0   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_init)
    [Anonymous Symbol]                       0x00001d08   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    __arm_cp.1_0                             0x00001d30   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    __arm_cp.1_1                             0x00001d34   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    __arm_cp.1_2                             0x00001d38   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    __arm_cp.1_3                             0x00001d3c   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    __arm_cp.1_4                             0x00001d40   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    __arm_cp.1_5                             0x00001d44   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    [Anonymous Symbol]                       0x00001d48   Section        0  empty.o(.text.main)
    __arm_cp.0_0                             0x00001e9c   Number         4  empty.o(.text.main)
    __arm_cp.0_1                             0x00001ea0   Number         4  empty.o(.text.main)
    __arm_cp.0_2                             0x00001ea4   Number         4  empty.o(.text.main)
    __arm_cp.0_3                             0x00001ea8   Number         4  empty.o(.text.main)
    __arm_cp.0_4                             0x00001eac   Number         4  empty.o(.text.main)
    __arm_cp.0_6                             0x00001ec0   Number         4  empty.o(.text.main)
    __arm_cp.0_7                             0x00001ec4   Number         4  empty.o(.text.main)
    __arm_cp.0_8                             0x00001ec8   Number         4  empty.o(.text.main)
    __arm_cp.0_10                            0x00001ee4   Number         4  empty.o(.text.main)
    i.__ARM_common_ll_muluu                  0x00001ee8   Section        0  btod.o(i.__ARM_common_ll_muluu)
    i.__ARM_fpclassify                       0x00001f18   Section        0  fpclassify.o(i.__ARM_fpclassify)
    i._is_digit                              0x00001f44   Section        0  __printf_wp.o(i._is_digit)
    locale$$code                             0x00001f54   Section       44  lc_numeric_c.o(locale$$code)
    x$fpl$printf1                            0x00001f80   Section       16  printf1.o(x$fpl$printf1)
    ddiv_reciptbl                            0x00001f90   Data         128  ddiv.o(.constdata)
    .constdata                               0x00001f90   Section      128  ddiv.o(.constdata)
    x$fpl$usenofp                            0x00001f90   Section        0  usenofp.o(x$fpl$usenofp)
    tenpwrs_x                                0x00002010   Data          60  bigflt0.o(.constdata)
    .constdata                               0x00002010   Section      148  bigflt0.o(.constdata)
    tenpwrs_i                                0x0000204c   Data          64  bigflt0.o(.constdata)
    gADC_VOLTAGEClockConfig                  0x000020a4   Data           8  ti_msp_dl_config.o(.rodata.gADC_VOLTAGEClockConfig)
    [Anonymous Symbol]                       0x000020a4   Section        0  ti_msp_dl_config.o(.rodata.gADC_VOLTAGEClockConfig)
    gUART_0ClockConfig                       0x000020ac   Data           2  ti_msp_dl_config.o(.rodata.gUART_0ClockConfig)
    [Anonymous Symbol]                       0x000020ac   Section        0  ti_msp_dl_config.o(.rodata.gUART_0ClockConfig)
    gUART_0Config                            0x000020ae   Data          10  ti_msp_dl_config.o(.rodata.gUART_0Config)
    [Anonymous Symbol]                       0x000020ae   Section        0  ti_msp_dl_config.o(.rodata.gUART_0Config)
    locale$$data                             0x000020c8   Section       28  lc_numeric_c.o(locale$$data)
    __lcnum_c_name                           0x000020cc   Data           2  lc_numeric_c.o(locale$$data)
    __lcnum_c_start                          0x000020d4   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_point                          0x000020e0   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_thousands                      0x000020e2   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_grouping                       0x000020e3   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_end                            0x000020e4   Data           0  lc_numeric_c.o(locale$$data)
    .bss                                     0x20200000   Section       96  libspace.o(.bss)
    Heap_Mem                                 0x20200068   Data         256  startup_mspm0g350x_uvision.o(HEAP)
    HEAP                                     0x20200068   Section      256  startup_mspm0g350x_uvision.o(HEAP)
    Stack_Mem                                0x20200168   Data        4096  startup_mspm0g350x_uvision.o(STACK)
    STACK                                    0x20200168   Section     4096  startup_mspm0g350x_uvision.o(STACK)
    __initial_sp                             0x20201168   Data           0  startup_mspm0g350x_uvision.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv3M$S$PE$A:L22$X:L11$S22$IEEE1$IW$~IW$USESV6$~STKCKD$USESV7$~SHL$OTIME$ROPI$IEEEJ$EBA8$UX$STANDARDLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __Vectors                                0x00000000   Data           4  startup_mspm0g350x_uvision.o(RESET)
    _printf_flags                            0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_return_value                     0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_sizespec                         0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_widthprec                        0x00000000   Number         0  printf_stubs.o ABSOLUTE
    __ARM_exceptions_init                     - Undefined Weak Reference
    __alloca_initialize                       - Undefined Weak Reference
    __arm_preinit_                            - Undefined Weak Reference
    __arm_relocate_pie_                       - Undefined Weak Reference
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __sigvec_lookup                           - Undefined Weak Reference
    _atexit_init                              - Undefined Weak Reference
    _call_atexit_fns                          - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _fp_trap_init                             - Undefined Weak Reference
    _fp_trap_shutdown                         - Undefined Weak Reference
    _get_lc_collate                           - Undefined Weak Reference
    _get_lc_ctype                             - Undefined Weak Reference
    _get_lc_monetary                          - Undefined Weak Reference
    _get_lc_time                              - Undefined Weak Reference
    _getenv_init                              - Undefined Weak Reference
    _handle_redirection                       - Undefined Weak Reference
    _init_alloc                               - Undefined Weak Reference
    _init_user_alloc                          - Undefined Weak Reference
    _initio                                   - Undefined Weak Reference
    _printf_post_padding                      - Undefined Weak Reference
    _printf_pre_padding                       - Undefined Weak Reference
    _printf_truncate_signed                   - Undefined Weak Reference
    _printf_truncate_unsigned                 - Undefined Weak Reference
    _rand_init                                - Undefined Weak Reference
    _signal_finish                            - Undefined Weak Reference
    _signal_init                              - Undefined Weak Reference
    _terminate_alloc                          - Undefined Weak Reference
    _terminate_user_alloc                     - Undefined Weak Reference
    _terminateio                              - Undefined Weak Reference
    __Vectors_End                            0x000000c0   Data           0  startup_mspm0g350x_uvision.o(RESET)
    __Vectors_Size                           0x000000c0   Number         0  startup_mspm0g350x_uvision.o ABSOLUTE
    __main                                   0x000000c1   Thumb Code     8  __main.o(!!!main)
    __scatterload                            0x000000c9   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_rt2                        0x000000c9   Thumb Code    74  __scatter.o(!!!scatter)
    __scatterload_rt2_thumb_only             0x000000c9   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_loop                       0x000000d3   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_null                       0x00000121   Thumb Code     2  __scatter.o(!!handler_null)
    __scatterload_zeroinit                   0x00000129   Thumb Code    28  __scatter_zi.o(!!handler_zi)
    _printf_percent                          0x00000145   Thumb Code     0  _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000)
    _printf_f                                0x00000147   Thumb Code     0  _printf_f.o(.ARM.Collect$$_printf_percent$$00000003)
    _printf_d                                0x00000151   Thumb Code     0  _printf_d.o(.ARM.Collect$$_printf_percent$$00000009)
    _printf_percent_end                      0x0000015b   Thumb Code     0  _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017)
    __rt_lib_init                            0x0000015f   Thumb Code     0  libinit.o(.ARM.Collect$$libinit$$00000000)
    __rt_lib_init_fp_1                       0x00000161   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000002)
    __rt_lib_init_heap_1                     0x00000161   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    __rt_lib_init_lc_common                  0x00000161   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    __rt_lib_init_preinit_1                  0x00000161   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000006)
    __rt_lib_init_rand_1                     0x00000161   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000010)
    __rt_lib_init_relocate_pie_1             0x00000161   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    __rt_lib_init_user_alloc_1               0x00000161   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    __rt_lib_init_lc_collate_1               0x00000167   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    __rt_lib_init_lc_ctype_1                 0x00000167   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    __rt_lib_init_lc_monetary_1              0x00000167   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    __rt_lib_init_lc_numeric_2               0x00000167   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000018)
    __rt_lib_init_alloca_1                   0x00000171   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    __rt_lib_init_argv_1                     0x00000171   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    __rt_lib_init_atexit_1                   0x00000171   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    __rt_lib_init_clock_1                    0x00000171   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    __rt_lib_init_cpp_1                      0x00000171   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000034)
    __rt_lib_init_exceptions_1               0x00000171   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    __rt_lib_init_fp_trap_1                  0x00000171   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    __rt_lib_init_getenv_1                   0x00000171   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    __rt_lib_init_lc_numeric_1               0x00000171   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    __rt_lib_init_lc_time_1                  0x00000171   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    __rt_lib_init_return                     0x00000171   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000035)
    __rt_lib_init_signal_1                   0x00000171   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    __rt_lib_init_stdio_1                    0x00000171   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000027)
    __rt_lib_shutdown                        0x00000173   Thumb Code     0  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    __rt_lib_shutdown_cpp_1                  0x00000175   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    __rt_lib_shutdown_fp_trap_1              0x00000175   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000007)
    __rt_lib_shutdown_heap_1                 0x00000175   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F)
    __rt_lib_shutdown_return                 0x00000175   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000010)
    __rt_lib_shutdown_signal_1               0x00000175   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A)
    __rt_lib_shutdown_stdio_1                0x00000175   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    __rt_lib_shutdown_user_alloc_1           0x00000175   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    __rt_entry                               0x00000177   Thumb Code     0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    __rt_entry_presh_1                       0x00000177   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    __rt_entry_sh                            0x00000177   Thumb Code     0  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    __rt_entry_li                            0x0000017d   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    __rt_entry_postsh_1                      0x0000017d   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    __rt_entry_main                          0x00000181   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    __rt_entry_postli_1                      0x00000181   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    __rt_exit                                0x00000189   Thumb Code     0  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    __rt_exit_ls                             0x0000018b   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    __rt_exit_prels_1                        0x0000018b   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    __rt_exit_exit                           0x0000018f   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    Reset_Handler                            0x00000195   Thumb Code     4  startup_mspm0g350x_uvision.o(.text)
    NMI_Handler                              0x00000199   Thumb Code     2  startup_mspm0g350x_uvision.o(.text)
    HardFault_Handler                        0x0000019b   Thumb Code     2  startup_mspm0g350x_uvision.o(.text)
    SVC_Handler                              0x0000019d   Thumb Code     2  startup_mspm0g350x_uvision.o(.text)
    PendSV_Handler                           0x0000019f   Thumb Code     2  startup_mspm0g350x_uvision.o(.text)
    SysTick_Handler                          0x000001a1   Thumb Code     2  startup_mspm0g350x_uvision.o(.text)
    ADC1_IRQHandler                          0x000001a3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    AES_IRQHandler                           0x000001a3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    CANFD0_IRQHandler                        0x000001a3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    DAC0_IRQHandler                          0x000001a3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    DMA_IRQHandler                           0x000001a3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    Default_Handler                          0x000001a3   Thumb Code     2  startup_mspm0g350x_uvision.o(.text)
    GROUP0_IRQHandler                        0x000001a3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    GROUP1_IRQHandler                        0x000001a3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    I2C0_IRQHandler                          0x000001a3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    I2C1_IRQHandler                          0x000001a3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    RTC_IRQHandler                           0x000001a3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    SPI0_IRQHandler                          0x000001a3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    SPI1_IRQHandler                          0x000001a3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    TIMA0_IRQHandler                         0x000001a3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    TIMA1_IRQHandler                         0x000001a3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    TIMG0_IRQHandler                         0x000001a3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    TIMG12_IRQHandler                        0x000001a3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    TIMG6_IRQHandler                         0x000001a3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    TIMG7_IRQHandler                         0x000001a3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    TIMG8_IRQHandler                         0x000001a3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    UART0_IRQHandler                         0x000001a3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    UART1_IRQHandler                         0x000001a3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    UART2_IRQHandler                         0x000001a3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    UART3_IRQHandler                         0x000001a3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    __user_initial_stackheap                 0x000001a5   Thumb Code    10  startup_mspm0g350x_uvision.o(.text)
    __2sprintf                               0x000001c5   Thumb Code    36  noretval__2sprintf.o(.text)
    _printf_int_dec                          0x000001ed   Thumb Code    90  _printf_dec.o(.text)
    __printf                                 0x00000259   Thumb Code   270  __printf_wp.o(.text)
    _memset_w                                0x00000367   Thumb Code    26  rt_memclr.o(.text)
    _memset                                  0x00000381   Thumb Code    30  rt_memclr.o(.text)
    __aeabi_memclr                           0x0000039f   Thumb Code     4  rt_memclr.o(.text)
    __rt_memclr                              0x0000039f   Thumb Code     0  rt_memclr.o(.text)
    __aeabi_memclr4                          0x000003a3   Thumb Code     0  rt_memclr.o(.text)
    __aeabi_memclr8                          0x000003a3   Thumb Code     0  rt_memclr.o(.text)
    __rt_memclr_w                            0x000003a3   Thumb Code     4  rt_memclr.o(.text)
    __aeabi_uidivmod                         0x000003a9   Thumb Code    28  aeabi_sdivfast.o(.text)
    __aeabi_idivmod                          0x000003c5   Thumb Code   472  aeabi_sdivfast.o(.text)
    __use_two_region_memory                  0x000005a1   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_escrow$2region                 0x000005a3   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_expand$2region                 0x000005a5   Thumb Code     2  heapauxi.o(.text)
    __aeabi_d2f                              0x000005a9   Thumb Code     0  d2f.o(.text)
    _d2f                                     0x000005a9   Thumb Code   120  d2f.o(.text)
    __aeabi_ddiv                             0x00000625   Thumb Code     0  ddiv.o(.text)
    _ddiv                                    0x00000625   Thumb Code  1072  ddiv.o(.text)
    _drdiv                                   0x00000a55   Thumb Code    20  ddiv.o(.text)
    __aeabi_i2d_normalise                    0x00000a6d   Thumb Code    66  dflti.o(.text)
    __aeabi_i2d                              0x00000aaf   Thumb Code    16  dflti.o(.text)
    _dflt                                    0x00000aaf   Thumb Code     0  dflti.o(.text)
    __aeabi_ui2d                             0x00000abf   Thumb Code     6  dflti.o(.text)
    _dfltu                                   0x00000abf   Thumb Code     0  dflti.o(.text)
    __aeabi_dmul                             0x00000ac5   Thumb Code     0  dmul.o(.text)
    _dmul                                    0x00000ac5   Thumb Code   558  dmul.o(.text)
    __aeabi_f2d                              0x00000d0d   Thumb Code     0  f2d.o(.text)
    _f2d                                     0x00000d0d   Thumb Code    80  f2d.o(.text)
    _printf_int_common                       0x00000d61   Thumb Code   176  _printf_intcommon.o(.text)
    _printf_fp_dec_real                      0x00000fad   Thumb Code   620  _printf_fp_dec.o(.text)
    _printf_char_common                      0x0000122f   Thumb Code    32  _printf_char_common.o(.text)
    _sputc                                   0x00001255   Thumb Code    10  _sputc.o(.text)
    __rt_udiv10                              0x0000125f   Thumb Code    40  rtudiv10.o(.text)
    __rt_locale                              0x00001289   Thumb Code     8  rt_locale_intlibspace.o(.text)
    _ll_udiv10                               0x00001291   Thumb Code   122  lludiv10.o(.text)
    _printf_fp_infnan                        0x0000130d   Thumb Code   120  _printf_fp_infnan.o(.text)
    _btod_etento                             0x00001395   Thumb Code   210  bigflt0.o(.text)
    _btod_d2e                                0x0000146d   Thumb Code    64  btod.o(.text)
    _btod_emul                               0x000018a1   Thumb Code    28  btod.o(.text)
    _btod_emuld                              0x000018bd   Thumb Code   144  btod.o(.text)
    _btod_ediv                               0x0000194d   Thumb Code    26  btod.o(.text)
    _btod_edivd                              0x00001967   Thumb Code   124  btod.o(.text)
    __user_libspace                          0x000019ed   Thumb Code     8  libspace.o(.text)
    __user_perproc_libspace                  0x000019ed   Thumb Code     0  libspace.o(.text)
    __user_perthread_libspace                0x000019ed   Thumb Code     0  libspace.o(.text)
    __user_setup_stackheap                   0x000019f5   Thumb Code    62  sys_stackheap_outer.o(.text)
    exit                                     0x00001a33   Thumb Code    16  exit.o(.text)
    strcmp                                   0x00001a45   Thumb Code   176  strcmpv6m.o(.text)
    _sys_exit                                0x00001af5   Thumb Code     8  sys_exit.o(.text)
    __I$use$semihosting                      0x00001b01   Thumb Code     0  use_no_semi.o(.text)
    __use_no_semihosting_swi                 0x00001b01   Thumb Code     2  use_no_semi.o(.text)
    __semihosting_library_function           0x00001b03   Thumb Code     0  indicate_semi.o(.text)
    ADC0_IRQHandler                          0x00001b05   Thumb Code    20  empty.o(.text.ADC0_IRQHandler)
    DL_ADC12_setClockConfig                  0x00001b1d   Thumb Code    64  dl_adc12.o(.text.DL_ADC12_setClockConfig)
    DL_Common_delayCycles                    0x00001b5d   Thumb Code    10  dl_common.o(.text.DL_Common_delayCycles)
    DL_UART_init                             0x00001b69   Thumb Code    72  dl_uart.o(.text.DL_UART_init)
    DL_UART_setClockConfig                   0x00001bb1   Thumb Code    18  dl_uart.o(.text.DL_UART_setClockConfig)
    DL_UART_transmitDataBlocking             0x00001bc5   Thumb Code    20  dl_uart.o(.text.DL_UART_transmitDataBlocking)
    SYSCFG_DL_ADC_VOLTAGE_init               0x00001bd9   Thumb Code    76  ti_msp_dl_config.o(.text.SYSCFG_DL_ADC_VOLTAGE_init)
    SYSCFG_DL_GPIO_init                      0x00001c41   Thumb Code    12  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    SYSCFG_DL_SYSCTL_init                    0x00001c55   Thumb Code    56  ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init)
    SYSCFG_DL_UART_0_init                    0x00001c95   Thumb Code    76  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init)
    SYSCFG_DL_init                           0x00001cf1   Thumb Code    24  ti_msp_dl_config.o(.text.SYSCFG_DL_init)
    SYSCFG_DL_initPower                      0x00001d09   Thumb Code    40  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    main                                     0x00001d49   Thumb Code   340  empty.o(.text.main)
    __ARM_common_ll_muluu                    0x00001ee9   Thumb Code    48  btod.o(i.__ARM_common_ll_muluu)
    __ARM_fpclassify                         0x00001f19   Thumb Code    40  fpclassify.o(i.__ARM_fpclassify)
    _is_digit                                0x00001f45   Thumb Code    14  __printf_wp.o(i._is_digit)
    _get_lc_numeric                          0x00001f55   Thumb Code    44  lc_numeric_c.o(locale$$code)
    _printf_fp_dec                           0x00001f81   Thumb Code    16  printf1.o(x$fpl$printf1)
    __I$use$fp                               0x00001f90   Number         0  usenofp.o(x$fpl$usenofp)
    Region$$Table$$Base                      0x000020b8   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x000020c8   Number         0  anon$$obj.o(Region$$Table)
    __libspace_start                         0x20200000   Data          96  libspace.o(.bss)
    __temporary_stack_top$libspace           0x20200060   Data           0  libspace.o(.bss)
    gCheckADC                                0x20200060   Data           1  empty.o(.bss.gCheckADC)



==============================================================================

Memory Map of the image

  Image Entry point : 0x000000c1

  Load Region LR_IROM1 (Base: 0x00000000, Size: 0x000020e4, Max: 0x00020000, ABSOLUTE)

    Execution Region ER_IROM1 (Exec base: 0x00000000, Load base: 0x00000000, Size: 0x000020e4, Max: 0x00020000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x00000000   0x00000000   0x000000c0   Data   RO           21    RESET               startup_mspm0g350x_uvision.o
    0x000000c0   0x000000c0   0x00000008   Code   RO          157  * !!!main             c_p.l(__main.o)
    0x000000c8   0x000000c8   0x00000054   Code   RO          394    !!!scatter          c_p.l(__scatter.o)
    0x0000011c   0x0000011c   0x00000004   PAD
    0x00000120   0x00000120   0x00000002   Code   RO          395    !!handler_null      c_p.l(__scatter.o)
    0x00000122   0x00000122   0x00000006   PAD
    0x00000128   0x00000128   0x0000001c   Code   RO          398    !!handler_zi        c_p.l(__scatter_zi.o)
    0x00000144   0x00000144   0x00000002   Code   RO          144    .ARM.Collect$$_printf_percent$$00000000  c_p.l(_printf_percent.o)
    0x00000146   0x00000146   0x0000000a   Code   RO          143    .ARM.Collect$$_printf_percent$$00000003  c_p.l(_printf_f.o)
    0x00000150   0x00000150   0x0000000a   Code   RO          142    .ARM.Collect$$_printf_percent$$00000009  c_p.l(_printf_d.o)
    0x0000015a   0x0000015a   0x00000004   Code   RO          187    .ARM.Collect$$_printf_percent$$00000017  c_p.l(_printf_percent_end.o)
    0x0000015e   0x0000015e   0x00000002   Code   RO          256    .ARM.Collect$$libinit$$00000000  c_p.l(libinit.o)
    0x00000160   0x00000160   0x00000000   Code   RO          258    .ARM.Collect$$libinit$$00000002  c_p.l(libinit2.o)
    0x00000160   0x00000160   0x00000000   Code   RO          260    .ARM.Collect$$libinit$$00000004  c_p.l(libinit2.o)
    0x00000160   0x00000160   0x00000000   Code   RO          262    .ARM.Collect$$libinit$$00000006  c_p.l(libinit2.o)
    0x00000160   0x00000160   0x00000000   Code   RO          265    .ARM.Collect$$libinit$$0000000C  c_p.l(libinit2.o)
    0x00000160   0x00000160   0x00000000   Code   RO          267    .ARM.Collect$$libinit$$0000000E  c_p.l(libinit2.o)
    0x00000160   0x00000160   0x00000000   Code   RO          269    .ARM.Collect$$libinit$$00000010  c_p.l(libinit2.o)
    0x00000160   0x00000160   0x00000006   Code   RO          270    .ARM.Collect$$libinit$$00000011  c_p.l(libinit2.o)
    0x00000166   0x00000166   0x00000000   Code   RO          272    .ARM.Collect$$libinit$$00000013  c_p.l(libinit2.o)
    0x00000166   0x00000166   0x00000000   Code   RO          274    .ARM.Collect$$libinit$$00000015  c_p.l(libinit2.o)
    0x00000166   0x00000166   0x00000000   Code   RO          276    .ARM.Collect$$libinit$$00000017  c_p.l(libinit2.o)
    0x00000166   0x00000166   0x0000000a   Code   RO          277    .ARM.Collect$$libinit$$00000018  c_p.l(libinit2.o)
    0x00000170   0x00000170   0x00000000   Code   RO          278    .ARM.Collect$$libinit$$00000019  c_p.l(libinit2.o)
    0x00000170   0x00000170   0x00000000   Code   RO          280    .ARM.Collect$$libinit$$0000001B  c_p.l(libinit2.o)
    0x00000170   0x00000170   0x00000000   Code   RO          282    .ARM.Collect$$libinit$$0000001D  c_p.l(libinit2.o)
    0x00000170   0x00000170   0x00000000   Code   RO          284    .ARM.Collect$$libinit$$0000001F  c_p.l(libinit2.o)
    0x00000170   0x00000170   0x00000000   Code   RO          286    .ARM.Collect$$libinit$$00000021  c_p.l(libinit2.o)
    0x00000170   0x00000170   0x00000000   Code   RO          288    .ARM.Collect$$libinit$$00000023  c_p.l(libinit2.o)
    0x00000170   0x00000170   0x00000000   Code   RO          290    .ARM.Collect$$libinit$$00000025  c_p.l(libinit2.o)
    0x00000170   0x00000170   0x00000000   Code   RO          292    .ARM.Collect$$libinit$$00000027  c_p.l(libinit2.o)
    0x00000170   0x00000170   0x00000000   Code   RO          296    .ARM.Collect$$libinit$$0000002E  c_p.l(libinit2.o)
    0x00000170   0x00000170   0x00000000   Code   RO          298    .ARM.Collect$$libinit$$00000030  c_p.l(libinit2.o)
    0x00000170   0x00000170   0x00000000   Code   RO          300    .ARM.Collect$$libinit$$00000032  c_p.l(libinit2.o)
    0x00000170   0x00000170   0x00000000   Code   RO          302    .ARM.Collect$$libinit$$00000034  c_p.l(libinit2.o)
    0x00000170   0x00000170   0x00000002   Code   RO          303    .ARM.Collect$$libinit$$00000035  c_p.l(libinit2.o)
    0x00000172   0x00000172   0x00000002   Code   RO          359    .ARM.Collect$$libshutdown$$00000000  c_p.l(libshutdown.o)
    0x00000174   0x00000174   0x00000000   Code   RO          377    .ARM.Collect$$libshutdown$$00000002  c_p.l(libshutdown2.o)
    0x00000174   0x00000174   0x00000000   Code   RO          379    .ARM.Collect$$libshutdown$$00000004  c_p.l(libshutdown2.o)
    0x00000174   0x00000174   0x00000000   Code   RO          382    .ARM.Collect$$libshutdown$$00000007  c_p.l(libshutdown2.o)
    0x00000174   0x00000174   0x00000000   Code   RO          385    .ARM.Collect$$libshutdown$$0000000A  c_p.l(libshutdown2.o)
    0x00000174   0x00000174   0x00000000   Code   RO          387    .ARM.Collect$$libshutdown$$0000000C  c_p.l(libshutdown2.o)
    0x00000174   0x00000174   0x00000000   Code   RO          390    .ARM.Collect$$libshutdown$$0000000F  c_p.l(libshutdown2.o)
    0x00000174   0x00000174   0x00000002   Code   RO          391    .ARM.Collect$$libshutdown$$00000010  c_p.l(libshutdown2.o)
    0x00000176   0x00000176   0x00000000   Code   RO          172    .ARM.Collect$$rtentry$$00000000  c_p.l(__rtentry.o)
    0x00000176   0x00000176   0x00000000   Code   RO          192    .ARM.Collect$$rtentry$$00000002  c_p.l(__rtentry2.o)
    0x00000176   0x00000176   0x00000006   Code   RO          204    .ARM.Collect$$rtentry$$00000004  c_p.l(__rtentry4.o)
    0x0000017c   0x0000017c   0x00000000   Code   RO          194    .ARM.Collect$$rtentry$$00000009  c_p.l(__rtentry2.o)
    0x0000017c   0x0000017c   0x00000004   Code   RO          195    .ARM.Collect$$rtentry$$0000000A  c_p.l(__rtentry2.o)
    0x00000180   0x00000180   0x00000000   Code   RO          197    .ARM.Collect$$rtentry$$0000000C  c_p.l(__rtentry2.o)
    0x00000180   0x00000180   0x00000008   Code   RO          198    .ARM.Collect$$rtentry$$0000000D  c_p.l(__rtentry2.o)
    0x00000188   0x00000188   0x00000002   Code   RO          305    .ARM.Collect$$rtexit$$00000000  c_p.l(rtexit.o)
    0x0000018a   0x0000018a   0x00000000   Code   RO          324    .ARM.Collect$$rtexit$$00000002  c_p.l(rtexit2.o)
    0x0000018a   0x0000018a   0x00000004   Code   RO          325    .ARM.Collect$$rtexit$$00000003  c_p.l(rtexit2.o)
    0x0000018e   0x0000018e   0x00000006   Code   RO          326    .ARM.Collect$$rtexit$$00000004  c_p.l(rtexit2.o)
    0x00000194   0x00000194   0x00000030   Code   RO           22    .text               startup_mspm0g350x_uvision.o
    0x000001c4   0x000001c4   0x00000028   Code   RO          116    .text               c_p.l(noretval__2sprintf.o)
    0x000001ec   0x000001ec   0x0000006c   Code   RO          120    .text               c_p.l(_printf_dec.o)
    0x00000258   0x00000258   0x0000010e   Code   RO          130    .text               c_p.l(__printf_wp.o)
    0x00000366   0x00000366   0x00000040   Code   RO          145    .text               c_p.l(rt_memclr.o)
    0x000003a6   0x000003a6   0x00000002   PAD
    0x000003a8   0x000003a8   0x000001f8   Code   RO          147    .text               c_p.l(aeabi_sdivfast.o)
    0x000005a0   0x000005a0   0x00000006   Code   RO          155    .text               c_p.l(heapauxi.o)
    0x000005a6   0x000005a6   0x00000002   PAD
    0x000005a8   0x000005a8   0x0000007c   Code   RO          159    .text               fz_ps.l(d2f.o)
    0x00000624   0x00000624   0x00000448   Code   RO          161    .text               fz_ps.l(ddiv.o)
    0x00000a6c   0x00000a6c   0x00000058   Code   RO          164    .text               fz_ps.l(dflti.o)
    0x00000ac4   0x00000ac4   0x00000248   Code   RO          166    .text               fz_ps.l(dmul.o)
    0x00000d0c   0x00000d0c   0x00000054   Code   RO          168    .text               fz_ps.l(f2d.o)
    0x00000d60   0x00000d60   0x000000b0   Code   RO          177    .text               c_p.l(_printf_intcommon.o)
    0x00000e10   0x00000e10   0x00000414   Code   RO          179    .text               c_p.l(_printf_fp_dec.o)
    0x00001224   0x00001224   0x00000030   Code   RO          183    .text               c_p.l(_printf_char_common.o)
    0x00001254   0x00001254   0x0000000a   Code   RO          185    .text               c_p.l(_sputc.o)
    0x0000125e   0x0000125e   0x00000028   Code   RO          188    .text               c_p.l(rtudiv10.o)
    0x00001286   0x00001286   0x00000002   PAD
    0x00001288   0x00001288   0x00000008   Code   RO          211    .text               c_p.l(rt_locale_intlibspace.o)
    0x00001290   0x00001290   0x0000007a   Code   RO          213    .text               c_p.l(lludiv10.o)
    0x0000130a   0x0000130a   0x00000002   PAD
    0x0000130c   0x0000130c   0x00000088   Code   RO          215    .text               c_p.l(_printf_fp_infnan.o)
    0x00001394   0x00001394   0x000000d8   Code   RO          219    .text               c_p.l(bigflt0.o)
    0x0000146c   0x0000146c   0x00000580   Code   RO          222    .text               c_p.l(btod.o)
    0x000019ec   0x000019ec   0x00000008   Code   RO          235    .text               c_p.l(libspace.o)
    0x000019f4   0x000019f4   0x0000003e   Code   RO          238    .text               c_p.l(sys_stackheap_outer.o)
    0x00001a32   0x00001a32   0x00000010   Code   RO          243    .text               c_p.l(exit.o)
    0x00001a42   0x00001a42   0x00000002   PAD
    0x00001a44   0x00001a44   0x000000b0   Code   RO          249    .text               c_p.l(strcmpv6m.o)
    0x00001af4   0x00001af4   0x0000000c   Code   RO          319    .text               c_p.l(sys_exit.o)
    0x00001b00   0x00001b00   0x00000002   Code   RO          346    .text               c_p.l(use_no_semi.o)
    0x00001b02   0x00001b02   0x00000000   Code   RO          348    .text               c_p.l(indicate_semi.o)
    0x00001b02   0x00001b02   0x00000002   PAD
    0x00001b04   0x00001b04   0x00000018   Code   RO            8    .text.ADC0_IRQHandler  empty.o
    0x00001b1c   0x00001b1c   0x00000040   Code   RO           52    .text.DL_ADC12_setClockConfig  driverlib.a(dl_adc12.o)
    0x00001b5c   0x00001b5c   0x0000000a   Code   RO           64    .text.DL_Common_delayCycles  driverlib.a(dl_common.o)
    0x00001b66   0x00001b66   0x00000002   PAD
    0x00001b68   0x00001b68   0x00000048   Code   RO           73    .text.DL_UART_init  driverlib.a(dl_uart.o)
    0x00001bb0   0x00001bb0   0x00000012   Code   RO           75    .text.DL_UART_setClockConfig  driverlib.a(dl_uart.o)
    0x00001bc2   0x00001bc2   0x00000002   PAD
    0x00001bc4   0x00001bc4   0x00000014   Code   RO           87    .text.DL_UART_transmitDataBlocking  driverlib.a(dl_uart.o)
    0x00001bd8   0x00001bd8   0x00000068   Code   RO           39    .text.SYSCFG_DL_ADC_VOLTAGE_init  ti_msp_dl_config.o
    0x00001c40   0x00001c40   0x00000014   Code   RO           33    .text.SYSCFG_DL_GPIO_init  ti_msp_dl_config.o
    0x00001c54   0x00001c54   0x00000040   Code   RO           35    .text.SYSCFG_DL_SYSCTL_init  ti_msp_dl_config.o
    0x00001c94   0x00001c94   0x0000005c   Code   RO           37    .text.SYSCFG_DL_UART_0_init  ti_msp_dl_config.o
    0x00001cf0   0x00001cf0   0x00000018   Code   RO           29    .text.SYSCFG_DL_init  ti_msp_dl_config.o
    0x00001d08   0x00001d08   0x00000040   Code   RO           31    .text.SYSCFG_DL_initPower  ti_msp_dl_config.o
    0x00001d48   0x00001d48   0x000001a0   Code   RO            2    .text.main          empty.o
    0x00001ee8   0x00001ee8   0x00000030   Code   RO          224    i.__ARM_common_ll_muluu  c_p.l(btod.o)
    0x00001f18   0x00001f18   0x0000002c   Code   RO          233    i.__ARM_fpclassify  m_ps.l(fpclassify.o)
    0x00001f44   0x00001f44   0x0000000e   Code   RO          132    i._is_digit         c_p.l(__printf_wp.o)
    0x00001f52   0x00001f52   0x00000002   PAD
    0x00001f54   0x00001f54   0x0000002c   Code   RO          229    locale$$code        c_p.l(lc_numeric_c.o)
    0x00001f80   0x00001f80   0x00000010   Code   RO          170    x$fpl$printf1       fz_ps.l(printf1.o)
    0x00001f90   0x00001f90   0x00000000   Code   RO          190    x$fpl$usenofp       fz_ps.l(usenofp.o)
    0x00001f90   0x00001f90   0x00000080   Data   RO          162    .constdata          fz_ps.l(ddiv.o)
    0x00002010   0x00002010   0x00000094   Data   RO          220    .constdata          c_p.l(bigflt0.o)
    0x000020a4   0x000020a4   0x00000008   Data   RO           43    .rodata.gADC_VOLTAGEClockConfig  ti_msp_dl_config.o
    0x000020ac   0x000020ac   0x00000002   Data   RO           41    .rodata.gUART_0ClockConfig  ti_msp_dl_config.o
    0x000020ae   0x000020ae   0x0000000a   Data   RO           42    .rodata.gUART_0Config  ti_msp_dl_config.o
    0x000020b8   0x000020b8   0x00000010   Data   RO          393    Region$$Table       anon$$obj.o
    0x000020c8   0x000020c8   0x0000001c   Data   RO          228    locale$$data        c_p.l(lc_numeric_c.o)


    Execution Region RW_IRAM2 (Exec base: 0x20200000, Load base: 0x000020e4, Size: 0x00001168, Max: 0x00008000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20200000        -       0x00000060   Zero   RW          236    .bss                c_p.l(libspace.o)
    0x20200060        -       0x00000001   Zero   RW           10    .bss.gCheckADC      empty.o
    0x20200061   0x000020e4   0x00000007   PAD
    0x20200068        -       0x00000100   Zero   RW           20    HEAP                startup_mspm0g350x_uvision.o
    0x20200168        -       0x00001000   Zero   RW           19    STACK               startup_mspm0g350x_uvision.o



  Load Region LR_BCR (Base: 0x41c00000, Size: 0x00000000, Max: 0x00000100, ABSOLUTE)

    Execution Region BCR_CONFIG (Exec base: 0x41c00000, Load base: 0x41c00000, Size: 0x00000000, Max: 0x000000ff, ABSOLUTE)

    **** No section assigned to this execution region ****



  Load Region LR_BSL (Base: 0x41c00100, Size: 0x00000000, Max: 0x00000100, ABSOLUTE)

    Execution Region BSL_CONFIG (Exec base: 0x41c00100, Load base: 0x41c00100, Size: 0x00000000, Max: 0x00000080, ABSOLUTE)

    **** No section assigned to this execution region ****


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

       440         80          0          0          1       8373   empty.o
        48         22        192          0       4352        708   startup_mspm0g350x_uvision.o
       368         84         20          0          0      24026   ti_msp_dl_config.o

    ----------------------------------------------------------------------
       856        <USER>        <GROUP>          0       4360      33107   Object Totals
         0          0         16          0          0          0   (incl. Generated)
         0          0          0          0          7          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

         8          0          0          0          0         68   __main.o
       284          0          0          0          0        136   __printf_wp.o
         0          0          0          0          0          0   __rtentry.o
        12          0          0          0          0          0   __rtentry2.o
         6          0          0          0          0          0   __rtentry4.o
        86         10          0          0          0          0   __scatter.o
        28          0          0          0          0          0   __scatter_zi.o
        48          6          0          0          0         88   _printf_char_common.o
        10          0          0          0          0          0   _printf_d.o
       108         18          0          0          0         76   _printf_dec.o
        10          0          0          0          0          0   _printf_f.o
      1044         12          0          0          0        116   _printf_fp_dec.o
       136         16          0          0          0         76   _printf_fp_infnan.o
       176          0          0          0          0         84   _printf_intcommon.o
         2          0          0          0          0          0   _printf_percent.o
         4          0          0          0          0          0   _printf_percent_end.o
        10          0          0          0          0         60   _sputc.o
       504          4          0          0          0         92   aeabi_sdivfast.o
       216          6        148          0          0         80   bigflt0.o
      1456         30          0          0          0        336   btod.o
        16          0          0          0          0         68   exit.o
         6          0          0          0          0        136   heapauxi.o
         0          0          0          0          0          0   indicate_semi.o
        44         10         28          0          0         76   lc_numeric_c.o
         2          0          0          0          0          0   libinit.o
        18          0          0          0          0          0   libinit2.o
         2          0          0          0          0          0   libshutdown.o
         2          0          0          0          0          0   libshutdown2.o
         8          4          0          0         96         68   libspace.o
       122          0          0          0          0         72   lludiv10.o
        40          4          0          0          0         84   noretval__2sprintf.o
         8          4          0          0          0         68   rt_locale_intlibspace.o
        64          0          0          0          0        108   rt_memclr.o
         2          0          0          0          0          0   rtexit.o
        10          0          0          0          0          0   rtexit2.o
        40          0          0          0          0         60   rtudiv10.o
       176          4          0          0          0         80   strcmpv6m.o
        12          4          0          0          0         60   sys_exit.o
        62          0          0          0          0         80   sys_stackheap_outer.o
         2          0          0          0          0         68   use_no_semi.o
        64          8          0          0          0       4701   dl_adc12.o
        10          0          0          0          0        802   dl_common.o
       110         12          0          0          0      14128   dl_uart.o
       124          4          0          0          0         72   d2f.o
      1096         26        128          0          0        112   ddiv.o
        88          0          0          0          0         92   dflti.o
       584         26          0          0          0         84   dmul.o
        84          4          0          0          0         60   f2d.o
        16          4          0          0          0         76   printf1.o
         0          0          0          0          0          0   usenofp.o
        44          4          0          0          0         60   fpclassify.o

    ----------------------------------------------------------------------
      7032        <USER>        <GROUP>          0         96      22427   Library Totals
        28          4          0          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

      4784        132        176          0         96       2240   c_p.l
       184         20          0          0          0      19631   driverlib.a
      1992         64        128          0          0        496   fz_ps.l
        44          4          0          0          0         60   m_ps.l

    ----------------------------------------------------------------------
      7032        <USER>        <GROUP>          0         96      22427   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

      7888        410        532          0       4456      54178   Grand Totals
      7888        410        532          0       4456      54178   ELF Image Totals
      7888        410        532          0          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                 8420 (   8.22kB)
    Total RW  Size (RW Data + ZI Data)              4456 (   4.35kB)
    Total ROM Size (Code + RO Data + RW Data)       8420 (   8.22kB)

==============================================================================

