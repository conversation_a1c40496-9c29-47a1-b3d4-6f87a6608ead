/*
 * Copyright (c) 2025, MSPM0G3507 GrayScale Line Following Module - Minimal Version
 * 最简化版本，确保编译通过
 * 
 * 作者: Alex (工程师)
 * 版本: v1.0-minimal
 * 日期: 2025-07-10
 */

#ifndef GRAYSCALE_MINIMAL_H
#define GRAYSCALE_MINIMAL_H

#include "ti_msp_dl_config.h"
#include <stdint.h>
#include <stdbool.h>

#ifdef __cplusplus
extern "C" {
#endif

// ================================
// 基本常量定义
// ================================
#define GRAYSCALE_SENSOR_COUNT          7       // 传感器数量
#define ADC_MAX_VALUE                   4095    // 12位ADC最大值
#define DETECTION_THRESHOLD             2000    // 检测阈值

// ================================
// 基本数据结构
// ================================
typedef struct {
    uint16_t values[GRAYSCALE_SENSOR_COUNT];    // 传感器值
    float position;                             // 线条位置
    bool detected;                              // 检测状态
} GrayScale_Data_t;

// ================================
// 基本函数声明
// ================================
int GrayScale_Init(void);
int GrayScale_ReadAll(GrayScale_Data_t* data);
uint16_t GrayScale_ReadSingle(uint8_t channel);
float GrayScale_CalcPosition(const GrayScale_Data_t* data);

#ifdef __cplusplus
}
#endif

#endif // GRAYSCALE_MINIMAL_H
