# 引脚配置指南 - 七路灰度循迹模块

## 🔌 您的硬件引脚配置

### 引脚映射表
```
传感器序号 | 引脚标识 | MSPM0引脚 | 信号类型 | 功能说明
---------|---------|----------|---------|----------
传感器1   | 1       | B12      | 数字GPIO | 灰度检测输入
传感器2   | 2       | B17      | 数字GPIO | 灰度检测输入  
传感器3   | 3       | B04      | 数字GPIO | 灰度检测输入
传感器4   | 4       | B01      | 数字GPIO | 灰度检测输入
传感器5   | 5       | A28      | 模拟ADC  | 灰度检测输入
传感器6   | 6       | A31      | 模拟ADC  | 灰度检测输入
传感器7   | 7       | B15      | 数字GPIO | 灰度检测输入
电源     | +5V     | +5V      | 电源     | 模块供电
地线     | GND     | GND      | 地线     | 公共地
```

## 🔧 代码适配说明

### 引脚配置结构
```c
// 引脚配置表 - 对应您的硬件连接
static const sensor_pin_config_t pin_config[7] = {
    {0, false, GPIOB_BASE, DL_GPIO_PIN_12},  // 传感器1: B12 (数字)
    {1, false, GPIOB_BASE, DL_GPIO_PIN_17},  // 传感器2: B17 (数字)
    {2, false, GPIOB_BASE, DL_GPIO_PIN_4},   // 传感器3: B04 (数字)
    {3, false, GPIOB_BASE, DL_GPIO_PIN_1},   // 传感器4: B01 (数字)
    {4, true,  0, 0},                        // 传感器5: A28 (ADC)
    {5, true,  0, 0},                        // 传感器6: A31 (ADC)
    {6, false, GPIOB_BASE, DL_GPIO_PIN_15}   // 传感器7: B15 (数字)
};
```

### 读取逻辑适配
```c
// 数字GPIO读取 (B12, B17, B04, B01, B15)
bool pin_state = DL_GPIO_readPins(gpio_port, gpio_pin);
uint16_t value = pin_state ? 4095 : 0;  // 转换为0-4095范围

// 模拟ADC读取 (A28, A31)  
DL_ADC12_startConversion(ADC_VOLTAGE_INST);
uint16_t value = DL_ADC12_getMemResult(ADC_VOLTAGE_INST, ADC_CH);
```

## 📊 数据输出格式

### 调试输出格式
```
引脚标识版本:
B12[1234] B17[2345] B04[3456] B01[4095] A28[3456] A31[2345] B15[1234] | 位置:0.12 | 控制:1.2 | 检测:是

说明:
- B12[1234]: B12引脚读取值为1234
- A28[3456]: A28引脚ADC读取值为3456
- 位置: 计算出的线条位置 (-3.0 ~ +3.0)
- 控制: 控制输出值
- 检测: 是否检测到线条
```

## 🎯 使用步骤

### 步骤1: 硬件连接验证
```
请确认您的硬件连接:
✅ 传感器模块引脚1 -> MSPM0的B12
✅ 传感器模块引脚2 -> MSPM0的B17
✅ 传感器模块引脚3 -> MSPM0的B04
✅ 传感器模块引脚4 -> MSPM0的B01
✅ 传感器模块引脚5 -> MSPM0的A28
✅ 传感器模块引脚6 -> MSPM0的A31
✅ 传感器模块引脚7 -> MSPM0的B15
✅ +5V -> 电源正极
✅ GND -> 电源负极
```

### 步骤2: 代码部署
```bash
1. 在CCS项目中删除原有的empty.c
2. 添加 empty_pinmapped.c 到项目
3. 编译项目 (Ctrl+B)
4. 下载到开发板
5. 运行程序
```

### 步骤3: 功能验证
```
预期启动信息:
=== MSPM0G3507 七路灰度循迹系统启动 (引脚适配版) ===
引脚配置: B12,B17,B04,B01,A28,A31,B15
灰度系统初始化成功
开始七路传感器数据采集...
数据格式: B12[值] B17[值] B04[值] B01[值] A28[值] A31[值] B15[值]
```

## ⚙️ 配置说明

### 数字传感器特点 (B12,B17,B04,B01,B15)
- **输出类型**: 数字信号 (0V/3.3V)
- **检测逻辑**: 高电平=检测到线条, 低电平=检测到背景
- **代码处理**: 转换为0或4095的数值

### 模拟传感器特点 (A28,A31)
- **输出类型**: 模拟信号 (0-3.3V)
- **检测逻辑**: 电压值反映灰度强度
- **代码处理**: 直接读取ADC值 (0-4095)

### 阈值设置
```c
#define DETECTION_THRESHOLD 2000    // 检测阈值
// 数值 > 2000 认为检测到线条
// 数值 < 2000 认为检测到背景
```

## 🔧 调试技巧

### 1. 单独测试每个传感器
```c
// 在主循环中添加单独测试代码
for (int i = 0; i < 7; i++) {
    uint16_t value = GrayScale_ReadSingle(i);
    sprintf(buffer, "传感器%d: %d\r\n", i+1, value);
    uart0_send_string(buffer);
}
```

### 2. 检查引脚状态
```c
// 检查GPIO引脚配置
bool pin_state = DL_GPIO_readPins(GPIOB, DL_GPIO_PIN_12);
sprintf(buffer, "B12状态: %s\r\n", pin_state ? "高" : "低");
uart0_send_string(buffer);
```

### 3. ADC调试
```c
// 检查ADC工作状态
uint32_t adc_status = DL_ADC12_getStatus(ADC_VOLTAGE_INST);
sprintf(buffer, "ADC状态: 0x%08X\r\n", adc_status);
uart0_send_string(buffer);
```

## 📋 故障排除

### 问题1: 某些传感器读数异常
**可能原因**: 引脚配置错误或硬件连接问题
**解决方法**: 
1. 检查硬件连接
2. 验证引脚配置
3. 使用万用表测试信号

### 问题2: ADC传感器无读数
**可能原因**: ADC配置问题
**解决方法**:
1. 检查ti_msp_dl_config.c中的ADC配置
2. 确认A28,A31引脚配置为ADC功能
3. 检查ADC参考电压设置

### 问题3: 位置计算不准确
**可能原因**: 阈值设置不当
**解决方法**:
1. 调整DETECTION_THRESHOLD值
2. 根据实际传感器特性修改权重
3. 校准传感器响应

**现在您的代码已经完全适配您的引脚配置！** 🎉
