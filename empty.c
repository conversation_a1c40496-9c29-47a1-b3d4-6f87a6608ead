/*
 * Copyright (c) 2021, Texas Instruments Incorporated
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 *
 * *  Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 *
 * *  Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the distribution.
 *
 * *  Neither the name of Texas Instruments Incorporated nor the names of
 *    its contributors may be used to endorse or promote products derived
 *    from this software without specific prior written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
 * AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO,
 * THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR
 * PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR
 * CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, <PERSON>ECIAL,
 * EXEMPLARY, OR <PERSON>NS<PERSON>QUENTIAL DAMAGES (INCLUDING, BUT NOT <PERSON>IMITED TO,
 * PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS;
 * OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,
 * WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR
 * OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE,
 * EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 */

#include "ti_msp_dl_config.h"
#include "stdio.h"

#define delay_ms(X)	delay_cycles((CPUCLK_FREQ/1000)*(X))

void uart0_send_string(char* str);//??????
volatile bool gCheckADC;        //ADC???????
unsigned int adc_getValue(void);//??ADC???

int main(void)
{
		char show_buff[100]={0};
		unsigned int adc_value = 0;
		float voltage_value = 0;

		SYSCFG_DL_init();

		//??ADC??
		NVIC_EnableIRQ(ADC_VOLTAGE_INST_INT_IRQN);

		uart0_send_string("adc Demo start\r\n");
		while (1)
		{
				//??ADC??
				adc_value = adc_getValue();
				sprintf(show_buff,"adc value:%d\r\n", adc_value);
				uart0_send_string(show_buff);

				//?ADC??????????
				voltage_value = adc_value/4095.0*3.3;

				sprintf(show_buff,"voltage value:%.2f\r\n",voltage_value );
				uart0_send_string(show_buff);
				delay_ms(1000);
		}
}

//???????
void uart0_send_string(char* str)
{
	//??????????? ?? ?????????
	while(*str!=0&&str!=0)
	{
		//????????????,??????????????
		DL_UART_transmitDataBlocking(UART_0_INST,*str++);
	}
}

//??ADC???
unsigned int adc_getValue(void)
{
	unsigned int gAdcResult = 0;

	//????ADC????
	DL_ADC12_startConversion(ADC_VOLTAGE_INST);
	//???????????????????
	while (false == gCheckADC) {
			__WFE();
	}
	//????
	gAdcResult = DL_ADC12_getMemResult(ADC_VOLTAGE_INST, ADC_VOLTAGE_ADCMEM_ADC_CH0);

	//?????
	gCheckADC = false;

	return gAdcResult;
}

//ADC??????
void ADC_VOLTAGE_INST_IRQHandler(void)
{
	//?????ADC??
	switch (DL_ADC12_getPendingInterrupt(ADC_VOLTAGE_INST))
	{
		//??????????
		case DL_ADC12_IIDX_MEM0_RESULT_LOADED:
			gCheckADC = true;//?????1
			break;
		default:
			break;
	}
}