/*
 * Copyright (c) 2021, Texas Instruments Incorporated
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 *
 * *  Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 *
 * *  Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the distribution.
 *
 * *  Neither the name of Texas Instruments Incorporated nor the names of
 *    its contributors may be used to endorse or promote products derived
 *    from this software without specific prior written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
 * AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO,
 * THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR
 * PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR
 * CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, <PERSON>ECIAL,
 * EXEMPLARY, OR <PERSON>NS<PERSON>QUENTIAL DAMAGES (INCLUDING, BUT NOT <PERSON>IMITED TO,
 * PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS;
 * OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,
 * WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR
 * OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE,
 * EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 */

#include "ti_msp_dl_config.h"
#include "grayscale_minimal.h"
#include "stdio.h"

#define delay_ms(X)	DL_Common_delayCycles((CPUCLK_FREQ/1000)*(X))

// 函数声明
void uart0_send_string(char* str);
void display_sensor_data(const GrayScale_Data_t* data);

// 全局变量
static GrayScale_Data_t g_sensor_data;

int main(void)
{
    char show_buff[200] = {0};
    int result;

    // 系统初始化
    SYSCFG_DL_init();

    uart0_send_string("=== MSPM0G3507 七路灰度循迹系统启动 (最简版) ===\r\n");

    // 初始化灰度系统
    result = GrayScale_Init();
    if (result != 0) {
        uart0_send_string("灰度系统初始化失败\r\n");
        while(1); // 停止执行
    }

    uart0_send_string("灰度系统初始化成功\r\n");
    uart0_send_string("开始七路传感器数据采集...\r\n");
    uart0_send_string("数据格式: [S0] [S1] [S2] [S3] [S4] [S5] [S6] | 位置 | 检测状态\r\n");

    // 主循环
    while (1) {
        // 读取传感器数据
        result = GrayScale_ReadAll(&g_sensor_data);
        if (result == 0) {
            // 显示传感器数据
            display_sensor_data(&g_sensor_data);

            // 计算简单控制输出
            float control_output = g_sensor_data.position * 10.0f; // 简单比例控制

            // 显示控制结果
            sprintf(show_buff, "位置:%.2f | 控制:%.1f | 检测:%s\r\n",
                g_sensor_data.position,
                control_output,
                g_sensor_data.detected ? "是" : "否"
            );
            uart0_send_string(show_buff);
        } else {
            uart0_send_string("读取传感器数据失败\r\n");
        }

        // 延时，控制输出频率
        delay_ms(100); // 10Hz输出频率
    }
}

/**
 * @brief UART字符串发送函数
 * @param str 要发送的字符串
 */
void uart0_send_string(char* str)
{
    // 逐字符发送，直到遇到字符串结束符
    while(*str != 0 && str != 0)
    {
        // 阻塞式发送单个字符
        DL_UART_transmitDataBlocking(UART_0_INST, *str++);
    }
}

/**
 * @brief 显示传感器原始数据
 * @param data 传感器数据
 */
void display_sensor_data(const GrayScale_Data_t* data)
{
    char show_buff[200] = {0};

    // 显示7路传感器的原始ADC值
    sprintf(show_buff, "[%4d] [%4d] [%4d] [%4d] [%4d] [%4d] [%4d] | ",
        data->values[0],
        data->values[1],
        data->values[2],
        data->values[3],
        data->values[4],
        data->values[5],
        data->values[6]
    );
    uart0_send_string(show_buff);
}

// 注意: ADC中断处理函数现在由grayscale_line_following.c中的ADC0_IRQHandler处理