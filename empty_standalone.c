/*
 * MSPM0G3507 七路灰度循迹模块 - 完全自包含版本
 * 所有代码都在一个文件中，避免链接错误
 * 
 * 作者: Alex (工程师)
 * 版本: v1.0-standalone
 * 日期: 2025-07-10
 */

#include "ti_msp_dl_config.h"
#include <stdio.h>
#include <stdbool.h>
#include <stdint.h>

// ================================
// 延时宏定义
// ================================
#define delay_ms(X)	DL_Common_delayCycles((CPUCLK_FREQ/1000)*(X))

// ================================
// 灰度传感器常量定义
// ================================
#define GRAYSCALE_SENSOR_COUNT          7       // 传感器数量
#define ADC_MAX_VALUE                   4095    // 12位ADC最大值
#define DETECTION_THRESHOLD             2000    // 检测阈值

// ================================
// 引脚配置定义 - 根据您的硬件连接
// ================================
// 传感器引脚映射 (按您提供的顺序)
#define SENSOR_1_PORT    GPIOB          // B12
#define SENSOR_1_PIN     DL_GPIO_PIN_12

#define SENSOR_2_PORT    GPIOB          // B17
#define SENSOR_2_PIN     DL_GPIO_PIN_17

#define SENSOR_3_PORT    GPIOB          // B04
#define SENSOR_3_PIN     DL_GPIO_PIN_4

#define SENSOR_4_PORT    GPIOB          // B01
#define SENSOR_4_PIN     DL_GPIO_PIN_1

// ADC引脚 (A28, A31)
#define SENSOR_5_ADC_CHANNEL    DL_ADC12_INPUT_CHAN_5  // A28对应的ADC通道
#define SENSOR_6_ADC_CHANNEL    DL_ADC12_INPUT_CHAN_6  // A31对应的ADC通道

#define SENSOR_7_PORT    GPIOB          // B15
#define SENSOR_7_PIN     DL_GPIO_PIN_15

// ================================
// 数据结构定义
// ================================
typedef struct {
    uint16_t values[GRAYSCALE_SENSOR_COUNT];    // 传感器值
    float position;                             // 线条位置
    bool detected;                              // 检测状态
} GrayScale_Data_t;

// ================================
// 全局变量
// ================================
static GrayScale_Data_t g_sensor_data;

// ================================
// 灰度传感器函数实现
// ================================

/**
 * @brief 初始化灰度系统 - 配置您的具体引脚
 */
int GrayScale_Init(void) {
    // GPIO引脚配置为输入模式 (数字传感器)
    // 注意: 这些配置可能已经在ti_msp_dl_config.c中完成
    // 如果已配置则无需重复配置

    // 传感器1: B12 - 配置为输入
    DL_GPIO_initDigitalInput(SENSOR_1_PIN);
    DL_GPIO_setPins(SENSOR_1_PORT, SENSOR_1_PIN);

    // 传感器2: B17 - 配置为输入
    DL_GPIO_initDigitalInput(SENSOR_2_PIN);
    DL_GPIO_setPins(SENSOR_2_PORT, SENSOR_2_PIN);

    // 传感器3: B04 - 配置为输入
    DL_GPIO_initDigitalInput(SENSOR_3_PIN);
    DL_GPIO_setPins(SENSOR_3_PORT, SENSOR_3_PIN);

    // 传感器4: B01 - 配置为输入
    DL_GPIO_initDigitalInput(SENSOR_4_PIN);
    DL_GPIO_setPins(SENSOR_4_PORT, SENSOR_4_PIN);

    // 传感器5,6: A28,A31 - ADC引脚已在SYSCFG_DL_init()中配置

    // 传感器7: B15 - 配置为输入
    DL_GPIO_initDigitalInput(SENSOR_7_PIN);
    DL_GPIO_setPins(SENSOR_7_PORT, SENSOR_7_PIN);

    return 0;
}

/**
 * @brief 读取单个传感器 - 根据您的引脚配置
 */
uint16_t GrayScale_ReadSingle(uint8_t channel) {
    if (channel >= GRAYSCALE_SENSOR_COUNT) {
        return 0;
    }

    uint16_t value = 0;

    switch (channel) {
        case 0: // 传感器1: B12 (数字输入)
            value = DL_GPIO_readPins(SENSOR_1_PORT, SENSOR_1_PIN) ? 4095 : 0;
            break;

        case 1: // 传感器2: B17 (数字输入)
            value = DL_GPIO_readPins(SENSOR_2_PORT, SENSOR_2_PIN) ? 4095 : 0;
            break;

        case 2: // 传感器3: B04 (数字输入)
            value = DL_GPIO_readPins(SENSOR_3_PORT, SENSOR_3_PIN) ? 4095 : 0;
            break;

        case 3: // 传感器4: B01 (数字输入)
            value = DL_GPIO_readPins(SENSOR_4_PORT, SENSOR_4_PIN) ? 4095 : 0;
            break;

        case 4: // 传感器5: A28 (ADC输入)
            // 配置ADC通道
            DL_ADC12_configConversionMem(ADC_VOLTAGE_INST, DL_ADC12_MEM_IDX_0,
                SENSOR_5_ADC_CHANNEL, DL_ADC12_REFERENCE_VOLTAGE_VDDA,
                DL_ADC12_SAMPLE_TIMER_SOURCE_SCOMP0, DL_ADC12_AVERAGING_MODE_DISABLED,
                DL_ADC12_BURN_OUT_SOURCE_DISABLED, DL_ADC12_TRIGGER_MODE_AUTO_NEXT,
                DL_ADC12_WINDOWS_COMP_MODE_DISABLED);

            DL_ADC12_startConversion(ADC_VOLTAGE_INST);
            while (!DL_ADC12_isConversionComplete(ADC_VOLTAGE_INST)) {}
            value = DL_ADC12_getMemResult(ADC_VOLTAGE_INST, DL_ADC12_MEM_IDX_0);
            break;

        case 5: // 传感器6: A31 (ADC输入)
            // 配置ADC通道
            DL_ADC12_configConversionMem(ADC_VOLTAGE_INST, DL_ADC12_MEM_IDX_0,
                SENSOR_6_ADC_CHANNEL, DL_ADC12_REFERENCE_VOLTAGE_VDDA,
                DL_ADC12_SAMPLE_TIMER_SOURCE_SCOMP0, DL_ADC12_AVERAGING_MODE_DISABLED,
                DL_ADC12_BURN_OUT_SOURCE_DISABLED, DL_ADC12_TRIGGER_MODE_AUTO_NEXT,
                DL_ADC12_WINDOWS_COMP_MODE_DISABLED);

            DL_ADC12_startConversion(ADC_VOLTAGE_INST);
            while (!DL_ADC12_isConversionComplete(ADC_VOLTAGE_INST)) {}
            value = DL_ADC12_getMemResult(ADC_VOLTAGE_INST, DL_ADC12_MEM_IDX_0);
            break;

        case 6: // 传感器7: B15 (数字输入)
            value = DL_GPIO_readPins(SENSOR_7_PORT, SENSOR_7_PIN) ? 4095 : 0;
            break;

        default:
            value = 0;
            break;
    }

    return value;
}

/**
 * @brief 计算线条位置
 */
float GrayScale_CalcPosition(const GrayScale_Data_t* data) {
    if (!data) {
        return 0.0f;
    }
    
    // 简单的位置计算
    const float weights[GRAYSCALE_SENSOR_COUNT] = {
        -3.0f, -2.0f, -1.0f, 0.0f, 1.0f, 2.0f, 3.0f
    };
    
    float sum = 0.0f;
    float weight_sum = 0.0f;
    
    for (int i = 0; i < GRAYSCALE_SENSOR_COUNT; i++) {
        if (data->values[i] > DETECTION_THRESHOLD) {
            float w = (float)data->values[i] / ADC_MAX_VALUE;
            sum += weights[i] * w;
            weight_sum += w;
        }
    }
    
    if (weight_sum > 0.0f) {
        return sum / weight_sum;
    } else {
        return 0.0f;
    }
}

/**
 * @brief 读取所有传感器
 */
int GrayScale_ReadAll(GrayScale_Data_t* data) {
    if (!data) {
        return -1;
    }
    
    // 读取所有传感器 (简化版本，实际只读取一个通道)
    for (int i = 0; i < GRAYSCALE_SENSOR_COUNT; i++) {
        data->values[i] = GrayScale_ReadSingle(0); // 暂时都读取通道0
    }
    
    // 计算位置
    data->position = GrayScale_CalcPosition(data);
    
    // 检测线条
    data->detected = false;
    for (int i = 0; i < GRAYSCALE_SENSOR_COUNT; i++) {
        if (data->values[i] > DETECTION_THRESHOLD) {
            data->detected = true;
            break;
        }
    }
    
    return 0;
}

// ================================
// UART通信函数
// ================================

/**
 * @brief UART字符串发送函数
 */
void uart0_send_string(char* str) {
    while(*str != 0 && str != 0) {
        DL_UART_transmitDataBlocking(UART_0_INST, *str++);
    }
}

/**
 * @brief 显示传感器原始数据
 */
void display_sensor_data(const GrayScale_Data_t* data) {
    char show_buff[200] = {0};
    
    // 显示7路传感器的原始ADC值
    sprintf(show_buff, "[%4d] [%4d] [%4d] [%4d] [%4d] [%4d] [%4d] | ",
        data->values[0],
        data->values[1], 
        data->values[2],
        data->values[3],
        data->values[4],
        data->values[5],
        data->values[6]
    );
    uart0_send_string(show_buff);
}

// ================================
// 主程序
// ================================

int main(void) {
    char show_buff[200] = {0};
    int result;
    
    // 系统初始化
    SYSCFG_DL_init();
    
    uart0_send_string("=== MSPM0G3507 七路灰度循迹系统启动 (自包含版) ===\r\n");
    
    // 初始化灰度系统
    result = GrayScale_Init();
    if (result != 0) {
        uart0_send_string("灰度系统初始化失败\r\n");
        while(1); // 停止执行
    }
    
    uart0_send_string("灰度系统初始化成功\r\n");
    uart0_send_string("开始七路传感器数据采集...\r\n");
    uart0_send_string("数据格式: [S0] [S1] [S2] [S3] [S4] [S5] [S6] | 位置 | 检测状态\r\n");
    
    // 主循环
    while (1) {
        // 读取传感器数据
        result = GrayScale_ReadAll(&g_sensor_data);
        if (result == 0) {
            // 显示传感器数据
            display_sensor_data(&g_sensor_data);
            
            // 计算简单控制输出
            float control_output = g_sensor_data.position * 10.0f; // 简单比例控制
            
            // 显示控制结果
            sprintf(show_buff, "位置:%.2f | 控制:%.1f | 检测:%s\r\n",
                g_sensor_data.position,
                control_output,
                g_sensor_data.detected ? "是" : "否"
            );
            uart0_send_string(show_buff);
        } else {
            uart0_send_string("读取传感器数据失败\r\n");
        }
        
        // 延时，控制输出频率
        delay_ms(100); // 10Hz输出频率
    }
}
