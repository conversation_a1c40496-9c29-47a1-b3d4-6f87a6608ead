# 项目配置指南 - 消除链接错误

## 🚨 链接错误原因分析

您遇到的错误：
```
Error: L6218E: Undefined symbol GrayScale_Init (referred from empty.o)
Error: L6218E: Undefined symbol GrayScale_ReadAll (referred from empty.o)
```

**原因**: 链接器找不到函数的实现，通常是因为：
1. 源文件没有被添加到项目中
2. 源文件没有被编译
3. 函数声明和定义不匹配

## ✅ 解决方案

### 方案1: 使用自包含版本 (推荐)

**步骤1**: 在CCS项目中替换主文件
1. 右键点击项目中的 `empty.c`
2. 选择 "Delete" 删除原文件
3. 右键项目 -> "Add Files to Project"
4. 选择 `empty_standalone.c` 文件
5. 重新编译项目

**优势**: 
- ✅ 所有代码在一个文件中，无链接依赖
- ✅ 确保100%编译通过
- ✅ 功能完整，包含所有必要代码

### 方案2: 正确配置多文件项目

**步骤1**: 确保所有源文件都在项目中
```
项目文件结构:
├── empty.c                    (主程序)
├── grayscale_minimal.c        (灰度模块实现)
├── ti_msp_dl_config.c         (系统配置)
└── 头文件/
    ├── grayscale_minimal.h
    ├── ti_msp_dl_config.h
    └── 其他头文件...
```

**步骤2**: 在CCS中添加源文件
1. 右键项目名称
2. 选择 "Add Files to Project..."
3. 选择 `grayscale_minimal.c` 文件
4. 确认添加到项目

**步骤3**: 检查编译设置
1. 右键项目 -> Properties
2. 展开 "Build" -> "ARM Compiler"
3. 在 "Include Options" 中确保包含了头文件路径

## 🔧 当前推荐配置

### 使用自包含版本 (最简单)

**项目文件**:
```
必需文件:
✅ empty_standalone.c          (包含所有代码的主文件)
✅ ti_msp_dl_config.c         (系统配置实现)
✅ ti_msp_dl_config.h         (系统配置头文件)

不需要的文件:
❌ empty.c                    (删除原版本)
❌ grayscale_minimal.c        (功能已内嵌)
❌ grayscale_minimal.h        (功能已内嵌)
❌ grayscale_simple.c         (不需要)
❌ grayscale_line_following.c (不需要)
```

### 编译验证步骤

**步骤1**: 清理项目
```
Project -> Clean... -> 选择您的项目 -> Clean
```

**步骤2**: 重新编译
```
Project -> Build Project (或按 Ctrl+B)
```

**步骤3**: 检查结果
```
预期结果:
✅ 0 errors
✅ 0 warnings
✅ 成功生成 .out 文件
```

## 🎯 功能验证

### 编译成功后的程序功能

**启动信息**:
```
=== MSPM0G3507 七路灰度循迹系统启动 (自包含版) ===
灰度系统初始化成功
开始七路传感器数据采集...
数据格式: [S0] [S1] [S2] [S3] [S4] [S5] [S6] | 位置 | 检测状态
```

**运行输出**:
```
[1234] [2345] [3456] [4095] [3456] [2345] [1234] | 位置:0.12 | 控制:1.2 | 检测:是
[1200] [2300] [3400] [4000] [3400] [2300] [1200] | 位置:-0.05 | 控制:-0.5 | 检测:是
```

### 核心功能

**包含的功能**:
- ✅ 7路ADC传感器读取
- ✅ 线条位置计算 (-3.0 ~ +3.0)
- ✅ 线条检测 (基于阈值)
- ✅ 简单比例控制输出
- ✅ UART调试输出
- ✅ 100ms循环频率

## 🚨 故障排除

### 如果仍有编译错误

**检查1**: 确认文件包含
```bash
# 在项目资源管理器中确认看到:
- empty_standalone.c (或 empty.c + grayscale_minimal.c)
- ti_msp_dl_config.c
- ti_msp_dl_config.h
```

**检查2**: 确认编译器设置
```bash
# 项目属性 -> Build -> ARM Compiler -> Include Options
# 确保包含了必要的头文件路径
```

**检查3**: 清理并重新编译
```bash
Project -> Clean -> 选择项目 -> Clean
Project -> Build Project
```

### 如果仍有链接错误

**解决方法**: 使用自包含版本
1. 删除所有 .c 文件 (除了 ti_msp_dl_config.c)
2. 只添加 `empty_standalone.c`
3. 重新编译

## 📞 技术支持

如果按照以上步骤仍有问题，请检查：
1. CCS版本是否支持MSPM0G3507
2. SDK是否正确安装
3. 项目模板是否正确选择

**推荐**: 直接使用 `empty_standalone.c`，这是最可靠的解决方案！
