/*
 * Copyright (c) 2025, MSPM0G3507 GrayScale Line Following Module - Simplified Version
 * 简化实现，确保编译通过
 * 
 * 作者: Alex (工程师)
 * 版本: v1.0-simple
 * 日期: 2025-07-10
 */

#include "grayscale_simple.h"

// ================================
// 简化API实现
// ================================

/**
 * @brief 初始化简化灰度系统
 * @return 0: 成功, -1: 失败
 */
int GrayScale_Simple_Init(void) {
    // ADC已经在SYSCFG_DL_init()中初始化
    // 这里只做基本验证
    return 0;
}

/**
 * @brief 读取所有传感器数据
 * @param data 数据输出指针
 * @return 0: 成功, -1: 失败
 */
int GrayScale_Simple_ReadSensors(GrayScale_Simple_t* data) {
    if (!data) return -1;
    
    // 读取7路传感器数据
    for (int i = 0; i < GRAYSCALE_SENSOR_COUNT; i++) {
        data->sensor_values[i] = GrayScale_Simple_ReadChannel(i);
    }
    
    // 设置时间戳
    data->timestamp = DL_SYSCTL_getSystemTick();
    
    // 计算线条位置
    data->line_position = GrayScale_Simple_CalculatePosition(data);
    
    // 检测线条
    data->line_detected = GrayScale_Simple_IsLineDetected(data);
    
    return 0;
}

/**
 * @brief 计算线条位置
 * @param data 传感器数据
 * @return 线条位置 (-3.0 ~ +3.0)
 */
float GrayScale_Simple_CalculatePosition(const GrayScale_Simple_t* data) {
    if (!data) return 0.0f;
    
    // 传感器权重 (位置)
    const float weights[GRAYSCALE_SENSOR_COUNT] = {
        -3.0f, -2.0f, -1.0f, 0.0f, 1.0f, 2.0f, 3.0f
    };
    
    float weighted_sum = 0.0f;
    float total_weight = 0.0f;
    const uint16_t threshold = 2000; // 简单阈值
    
    // 加权平均计算
    for (int i = 0; i < GRAYSCALE_SENSOR_COUNT; i++) {
        if (data->sensor_values[i] > threshold) {
            float weight = (float)data->sensor_values[i] / ADC_MAX_VALUE;
            weighted_sum += weights[i] * weight;
            total_weight += weight;
        }
    }
    
    // 返回加权平均位置
    if (total_weight > 0.0f) {
        return weighted_sum / total_weight;
    } else {
        return 0.0f; // 没有检测到线条，返回中央位置
    }
}

/**
 * @brief 检测是否有线条
 * @param data 传感器数据
 * @return true: 检测到线条, false: 没有检测到
 */
bool GrayScale_Simple_IsLineDetected(const GrayScale_Simple_t* data) {
    if (!data) return false;
    
    const uint16_t threshold = 2000; // 简单阈值
    
    // 检查是否有传感器超过阈值
    for (int i = 0; i < GRAYSCALE_SENSOR_COUNT; i++) {
        if (data->sensor_values[i] > threshold) {
            return true;
        }
    }
    
    return false;
}

// ================================
// 辅助函数
// ================================

/**
 * @brief 简单的延时函数
 * @param ms 延时毫秒数
 */
void GrayScale_Simple_DelayMs(uint32_t ms) {
    DL_Common_delayCycles((CPUCLK_FREQ / 1000) * ms);
}

/**
 * @brief 打印传感器数据 (调试用)
 * @param data 传感器数据
 */
void GrayScale_Simple_PrintData(const GrayScale_Simple_t* data) {
    if (!data) return;
    
    // 这里可以添加UART输出代码
    // 由于printf可能不可用，暂时留空
}

/**
 * @brief 获取单个传感器值
 * @param channel 传感器通道 (0-6)
 * @return ADC值
 */
uint16_t GrayScale_Simple_GetSensorValue(uint8_t channel) {
    if (channel >= GRAYSCALE_SENSOR_COUNT) return 0;
    return GrayScale_Simple_ReadChannel(channel);
}

/**
 * @brief 简单的PID控制计算
 * @param position 当前位置
 * @param target 目标位置 (通常为0.0)
 * @return 控制输出
 */
float GrayScale_Simple_PIDControl(float position, float target) {
    static float last_error = 0.0f;
    static float integral = 0.0f;
    
    // PID参数 (可调整)
    const float kp = 2.0f;
    const float ki = 0.1f;
    const float kd = 0.5f;
    
    // 计算误差
    float error = position - target;
    
    // 积分项
    integral += error;
    
    // 积分限幅
    if (integral > 10.0f) integral = 10.0f;
    if (integral < -10.0f) integral = -10.0f;
    
    // 微分项
    float derivative = error - last_error;
    last_error = error;
    
    // PID输出
    float output = kp * error + ki * integral + kd * derivative;
    
    // 输出限幅
    if (output > 100.0f) output = 100.0f;
    if (output < -100.0f) output = -100.0f;
    
    return output;
}
