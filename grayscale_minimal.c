/*
 * Copyright (c) 2025, MSPM0G3507 GrayScale Line Following Module - Minimal Version
 * 最简化实现，确保编译通过
 * 
 * 作者: Alex (工程师)
 * 版本: v1.0-minimal
 * 日期: 2025-07-10
 */

#include "grayscale_minimal.h"

// ================================
// 基本函数实现
// ================================

/**
 * @brief 初始化灰度系统
 * @return 0: 成功, -1: 失败
 */
int GrayScale_Init(void) {
    // ADC已经在SYSCFG_DL_init()中初始化
    return 0;
}

/**
 * @brief 读取单个传感器
 * @param channel 通道号 (0-6)
 * @return ADC值
 */
uint16_t GrayScale_ReadSingle(uint8_t channel) {
    if (channel >= GRAYSCALE_SENSOR_COUNT) {
        return 0;
    }
    
    // 简单的ADC读取 - 使用现有的ADC配置
    DL_ADC12_startConversion(ADC_VOLTAGE_INST);
    
    // 简单延时等待转换完成
    volatile uint32_t delay = 1000;
    while (delay-- > 0) {
        // 等待
    }
    
    // 读取结果
    return DL_ADC12_getMemResult(ADC_VOLTAGE_INST, ADC_VOLTAGE_ADCMEM_ADC_CH0);
}

/**
 * @brief 读取所有传感器
 * @param data 数据输出
 * @return 0: 成功, -1: 失败
 */
int GrayScale_ReadAll(GrayScale_Data_t* data) {
    if (!data) {
        return -1;
    }
    
    // 读取所有传感器 (简化版本，实际只读取一个通道)
    for (int i = 0; i < GRAYSCALE_SENSOR_COUNT; i++) {
        data->values[i] = GrayScale_ReadSingle(0); // 暂时都读取通道0
    }
    
    // 计算位置
    data->position = GrayScale_CalcPosition(data);
    
    // 检测线条
    data->detected = false;
    for (int i = 0; i < GRAYSCALE_SENSOR_COUNT; i++) {
        if (data->values[i] > DETECTION_THRESHOLD) {
            data->detected = true;
            break;
        }
    }
    
    return 0;
}

/**
 * @brief 计算线条位置
 * @param data 传感器数据
 * @return 位置值 (-3.0 ~ +3.0)
 */
float GrayScale_CalcPosition(const GrayScale_Data_t* data) {
    if (!data) {
        return 0.0f;
    }
    
    // 简单的位置计算
    const float weights[GRAYSCALE_SENSOR_COUNT] = {
        -3.0f, -2.0f, -1.0f, 0.0f, 1.0f, 2.0f, 3.0f
    };
    
    float sum = 0.0f;
    float weight_sum = 0.0f;
    
    for (int i = 0; i < GRAYSCALE_SENSOR_COUNT; i++) {
        if (data->values[i] > DETECTION_THRESHOLD) {
            float w = (float)data->values[i] / ADC_MAX_VALUE;
            sum += weights[i] * w;
            weight_sum += w;
        }
    }
    
    if (weight_sum > 0.0f) {
        return sum / weight_sum;
    } else {
        return 0.0f;
    }
}
