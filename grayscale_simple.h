/*
 * Copyright (c) 2025, MSPM0G3507 GrayScale Line Following Module - Simplified Version
 * 简化版本，确保编译通过
 * 
 * 作者: Alex (工程师)
 * 版本: v1.0-simple
 * 日期: 2025-07-10
 */

#ifndef GRAYSCALE_SIMPLE_H
#define GRAYSCALE_SIMPLE_H

#include "ti_msp_dl_config.h"
#include <stdint.h>
#include <stdbool.h>

#ifdef __cplusplus
extern "C" {
#endif

// ================================
// 简化配置常量
// ================================
#define GRAYSCALE_SENSOR_COUNT          7       // 传感器数量
#define ADC_MAX_VALUE                   4095    // 12位ADC最大值

// ================================
// 简化数据结构
// ================================
typedef struct {
    uint16_t sensor_values[GRAYSCALE_SENSOR_COUNT];  // 传感器ADC值
    float line_position;                             // 线条位置 (-3.0 ~ +3.0)
    bool line_detected;                              // 线条检测状态
    uint32_t timestamp;                              // 时间戳
} GrayScale_Simple_t;

// ================================
// 简化API函数
// ================================
int GrayScale_Simple_Init(void);
int GrayScale_Simple_ReadSensors(GrayScale_Simple_t* data);
float GrayScale_Simple_CalculatePosition(const GrayScale_Simple_t* data);
bool GrayScale_Simple_IsLineDetected(const GrayScale_Simple_t* data);

// ================================
// 内联辅助函数
// ================================
static inline uint16_t GrayScale_Simple_ReadChannel(uint8_t channel) {
    if (channel >= GRAYSCALE_SENSOR_COUNT) return 0;
    
    // 配置ADC通道
    DL_ADC12_configConversionMem(ADC_VOLTAGE_INST, 
        DL_ADC12_MEM_IDX_0,
        (DL_ADC12_INPUT_CHAN_0 + channel),
        DL_ADC12_REFERENCE_VOLTAGE_VDDA,
        DL_ADC12_SAMPLE_TIMER_SOURCE_SCOMP0,
        DL_ADC12_AVERAGING_MODE_DISABLED,
        DL_ADC12_BURN_OUT_SOURCE_DISABLED,
        DL_ADC12_TRIGGER_MODE_AUTO_NEXT,
        DL_ADC12_WINDOWS_COMP_MODE_DISABLED
    );
    
    // 启动转换
    DL_ADC12_startConversion(ADC_VOLTAGE_INST);
    
    // 等待转换完成 (简单轮询)
    while (!DL_ADC12_isConversionComplete(ADC_VOLTAGE_INST)) {
        // 等待
    }
    
    // 读取结果
    return DL_ADC12_getMemResult(ADC_VOLTAGE_INST, DL_ADC12_MEM_IDX_0);
}

#ifdef __cplusplus
}
#endif

#endif // GRAYSCALE_SIMPLE_H
